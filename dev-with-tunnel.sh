#!/bin/bash

# TON SDK Development + Tunnel Script
# This script starts Vite server first, then sets up tunnels for both frontend and API

set -e

echo "🚀 Starting TON SDK development with tunnels..."

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

# Configuration
FRONTEND_PORT=5173
API_PORT=8000
MANIFEST_FILE="public/tonconnect-manifest.json"
ENV_FILE=".env"
BOT_ENV_FILE="../bot/.env"

# Function to cleanup on exit
cleanup() {
    echo -e "\n${YELLOW}🧹 Cleaning up all processes...${NC}"
    if [ ! -z "$DEV_PID" ]; then
        kill $DEV_PID 2>/dev/null || true
        echo "✅ Development server terminated"
    fi
    if [ ! -z "$FRONTEND_TUNNEL_PID" ]; then
        kill $FRONTEND_TUNNEL_PID 2>/dev/null || true
        echo "✅ Frontend tunnel terminated"
    fi
    if [ ! -z "$API_TUNNEL_PID" ]; then
        kill $API_TUNNEL_PID 2>/dev/null || true
        echo "✅ API tunnel terminated"
    fi
    # Kill any remaining processes on the ports
    pkill -f "vite.*$FRONTEND_PORT" 2>/dev/null || true
    pkill -f "cloudflared.*$FRONTEND_PORT" 2>/dev/null || true
    pkill -f "cloudflared.*$API_PORT" 2>/dev/null || true
}

# Set trap to cleanup on script exit
trap cleanup EXIT INT TERM

# Function to update configuration files
update_config_files() {
    local frontend_url=$1
    local api_url=$2
    echo "📝 Updating configuration files..."

    # Update tonconnect manifest
    if [ ! -f "$MANIFEST_FILE" ]; then
        mkdir -p public
    fi

    cat > "$MANIFEST_FILE" << EOF
{
  "url": "$frontend_url",
  "name": "TonConnect UI",
  "iconUrl": "$frontend_url/tonconnect-icon.png",
  "termsOfUseUrl": "$frontend_url/terms-of-use.txt",
  "privacyPolicyUrl": "$frontend_url/privacy-policy.txt"
}
EOF

    # Update app .env file with frontend, API, and mini app URLs
    echo "📝 Updating app .env file..."
    local temp_file=$(mktemp)
    if [ -f "$ENV_FILE" ]; then
        grep -v "^VITE_APP_URL=" "$ENV_FILE" | grep -v "^MINI_APP_URL=" | grep -v "^VITE_API_URL=" > "$temp_file" 2>/dev/null || true
    fi
    echo "VITE_APP_URL=$frontend_url" >> "$temp_file"
    echo "MINI_APP_URL=$frontend_url" >> "$temp_file"
    echo "VITE_API_URL=$api_url/api" >> "$temp_file"
    mv "$temp_file" "$ENV_FILE"
    echo -e "${GREEN}✅ Updated app .env file${NC}"

    # Update bot .env file
    echo "📝 Updating bot .env file..."
    if [ -f "$BOT_ENV_FILE" ]; then
        local bot_temp_file=$(mktemp)
        grep -v "^MINI_APP_URL=" "$BOT_ENV_FILE" > "$bot_temp_file" 2>/dev/null || true
        echo "MINI_APP_URL=$frontend_url" >> "$bot_temp_file"
        mv "$bot_temp_file" "$BOT_ENV_FILE"
        echo -e "${GREEN}✅ Updated bot .env file${NC}"
    else
        echo -e "${YELLOW}⚠️ Bot .env file not found, skipping bot configuration${NC}"
    fi

    # Restart bot processes
    restart_bot

    echo -e "${GREEN}✅ All configuration files updated${NC}"
}

# Function to restart bot if it's running
restart_bot() {
    echo "🤖 Checking for running bot processes..."

    # Look for bot processes
    local bot_pids=$(pgrep -f "bot" 2>/dev/null || true)

    if [ ! -z "$bot_pids" ]; then
        echo -e "${YELLOW}⚠️ Found running bot processes, attempting restart...${NC}"

        # Kill existing bot processes
        echo "$bot_pids" | xargs kill 2>/dev/null || true
        sleep 2

        # Try to start the bot if there's a start script
        if [ -f "../bot/package.json" ]; then
            echo "🚀 Starting Node.js bot..."
            (cd ../bot && npm start > /dev/null 2>&1 &) || echo -e "${YELLOW}⚠️ Could not auto-start bot. Please start it manually.${NC}"
        elif [ -f "../bot/main.py" ] || [ -f "../bot/bot.py" ]; then
            echo "🚀 Starting Python bot..."
            (cd ../bot && python3 main.py > /dev/null 2>&1 &) 2>/dev/null || \
            (cd ../bot && python3 bot.py > /dev/null 2>&1 &) 2>/dev/null || \
            echo -e "${YELLOW}⚠️ Could not auto-start bot. Please start it manually.${NC}"
        else
            echo -e "${YELLOW}⚠️ Bot restart completed. Please start your bot manually with the new URL.${NC}"
        fi
    else
        echo -e "${BLUE}ℹ️ No running bot processes found${NC}"
    fi
}

# Function to extract tunnel URL
extract_tunnel_url() {
    local output_file=$1
    local attempts=0
    local max_attempts=30

    while [ $attempts -lt $max_attempts ]; do
        if [ -f "$output_file" ]; then
            # Look for the Cloudflare tunnel URL pattern
            local url=$(grep -oP 'https://[a-zA-Z0-9-]+\.trycloudflare\.com' "$output_file" | head -1)
            if [ ! -z "$url" ]; then
                echo "$url"
                return 0
            fi
        fi
        sleep 1
        attempts=$((attempts + 1))
    done

    return 1
}

echo -e "${BLUE}🖥️ Step 1: Starting Vite development server...${NC}"

# Start the development server
vite --port $FRONTEND_PORT --host &
DEV_PID=$!

# Wait for server to start
echo "⏳ Waiting for Vite server to start..."
sleep 3

# Check if dev server is running
if ! kill -0 $DEV_PID 2>/dev/null; then
    echo -e "${RED}❌ Failed to start development server${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Vite server is running on port $FRONTEND_PORT${NC}"

echo -e "${BLUE}🔗 Step 2: Setting up tunnels...${NC}"

# Create temporary files for tunnel outputs
FRONTEND_TUNNEL_OUTPUT=$(mktemp)
API_TUNNEL_OUTPUT=$(mktemp)

# Start Cloudflare tunnel for frontend in background
echo "⏳ Starting frontend tunnel (port $FRONTEND_PORT)..."
cloudflared tunnel --url http://localhost:$FRONTEND_PORT > "$FRONTEND_TUNNEL_OUTPUT" 2>&1 &
FRONTEND_TUNNEL_PID=$!

# Start Cloudflare tunnel for API in background
echo "⏳ Starting API tunnel (port $API_PORT)..."
cloudflared tunnel --url http://localhost:$API_PORT > "$API_TUNNEL_OUTPUT" 2>&1 &
API_TUNNEL_PID=$!

echo "⏳ Establishing tunnel connections..."

# Extract the tunnel URLs
FRONTEND_TUNNEL_URL=$(extract_tunnel_url "$FRONTEND_TUNNEL_OUTPUT")
API_TUNNEL_URL=$(extract_tunnel_url "$API_TUNNEL_OUTPUT")

if [ $? -eq 0 ] && [ ! -z "$FRONTEND_TUNNEL_URL" ] && [ ! -z "$API_TUNNEL_URL" ]; then
    echo -e "\n${GREEN}🎉 Development environment is ready!${NC}"

    # Update configuration files
    update_config_files "$FRONTEND_TUNNEL_URL" "$API_TUNNEL_URL"

    echo -e "\n${BLUE}📍 Your URLs:${NC}"
    echo -e "   ${YELLOW}Frontend Local:${NC}  http://localhost:$FRONTEND_PORT"
    echo -e "   ${YELLOW}Frontend Tunnel:${NC} $FRONTEND_TUNNEL_URL"
    echo -e "   ${YELLOW}API Local:${NC}       http://localhost:$API_PORT"
    echo -e "   ${YELLOW}API Tunnel:${NC}      $API_TUNNEL_URL"
    echo -e "   ${YELLOW}API Endpoint:${NC}    $API_TUNNEL_URL/api"

    echo -e "\n${GREEN}✅ TON Connect is configured for: $FRONTEND_TUNNEL_URL${NC}"
    echo -e "${GREEN}✅ Bot is configured with MINI_APP_URL: $FRONTEND_TUNNEL_URL${NC}"
    echo -e "${GREEN}✅ VITE_API_URL is configured as: $API_TUNNEL_URL/api${NC}"
    echo -e "${YELLOW}📱 Use the tunnel URLs for mobile testing${NC}"
    echo -e "\n${YELLOW}⚠️ Keep this terminal open to maintain all services${NC}"
    echo -e "${YELLOW}⚠️ Make sure your API server is running on port $API_PORT${NC}"
    echo -e "${BLUE}Press Ctrl+C to stop everything${NC}"

    # Wait for either process to exit
    wait $DEV_PID $FRONTEND_TUNNEL_PID $API_TUNNEL_PID
else
    echo -e "${RED}❌ Failed to establish one or more tunnels${NC}"
    if [ -z "$FRONTEND_TUNNEL_URL" ]; then
        echo -e "${RED}❌ Frontend tunnel failed${NC}"
    fi
    if [ -z "$API_TUNNEL_URL" ]; then
        echo -e "${RED}❌ API tunnel failed${NC}"
    fi
    exit 1
fi

# Cleanup
rm -f "$FRONTEND_TUNNEL_OUTPUT" "$API_TUNNEL_OUTPUT"
