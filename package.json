{"name": "app", "type": "module", "version": "0.0.0", "private": true, "scripts": {"dev": "./dev-with-tunnel.sh", "dev:local": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview", "tunnel": "./setup-ton-dev.sh"}, "dependencies": {"@esbuild-plugins/node-globals-polyfill": "^0.2.3", "@tailwindcss/vite": "^4.1.8", "@tanstack/vue-table": "^8.21.3", "@ton/core": "^0.60.1", "@ton/ton": "^15.2.1", "@tonconnect/ui": "^2.1.0", "@vueuse/core": "^13.4.0", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "lucide-vue-next": "^0.515.0", "pinia": "^3.0.3", "reka-ui": "^2.3.1", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.8", "vue": "^3.5.13", "vue-i18n": "11", "vue-router": "^4.5.1", "vue-sonner": "^2.0.2"}, "devDependencies": {"@antfu/eslint-config": "^4.14.1", "@types/node": "^22.15.30", "@vitejs/plugin-vue": "^5.2.3", "@vue/tsconfig": "^0.7.0", "eslint": "^9.28.0", "tw-animate-css": "^1.3.4", "typescript": "~5.8.3", "vite": "^6.3.5", "vue-tsc": "^2.2.8"}, "packageManager": "pnpm@10.13.1+sha1.aa8c167c4509c97519542ef77a09e4b8ab59fb6a"}