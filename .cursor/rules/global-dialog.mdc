# Global Dialog Usage Guide

## Overview
Use the global dialog system for displaying success and error messages throughout the application. This provides a consistent user experience and centralized dialog management.

## Basic Usage

### Import the Composable
```typescript
import { useGlobalDialog } from '@/composables/useGlobalDialog'

const { showSuccess, showError } = useGlobalDialog()
```

### Show Success Dialog
```typescript
// Simple success message
showSuccess('Success!')

// Success with detailed message
showSuccess('Operation Successful', 'Your data has been saved successfully.')
```

### Show Error Dialog
```typescript
// Simple error message
showError('Error!')

// Error with detailed message
showError('Save Failed', 'Unable to save your data. Please check your connection and try again.')
```

## Implementation Details

### Files Structure
- **Composable**: `src/composables/useGlobalDialog.ts` - State management and methods
- **Component**: `src/components/shared/GlobalDialog.vue` - UI implementation
- **Integration**: `src/components/layout/AppLayout.vue` - Global availability

### Available Methods
- `showSuccess(title: string, message?: string)` - Display success dialog
- `showError(title: string, message?: string)` - Display error dialog
- `closeDialog()` - Manually close dialog (auto-closes on OK button)

## Best Practices

### When to Use
- API operation results (success/failure)
- Form submission feedback
- File upload/download status
- User action confirmations
- Error notifications

### Message Guidelines
- **Title**: Keep concise (2-4 words)
- **Message**: Provide actionable information
- **Success**: Focus on what was accomplished
- **Error**: Include next steps or troubleshooting hints

### Examples

#### API Operations
```typescript
// After successful API call
showSuccess('Data Saved', 'Your profile has been updated successfully.')

// After failed API call
showError('Save Failed', 'Unable to connect to server. Please try again.')
```

#### Form Validation
```typescript
// Form submission success
showSuccess('Form Submitted', 'We will contact you within 24 hours.')

// Form validation error
showError('Invalid Data', 'Please check all required fields and try again.')
```

#### File Operations
```typescript
// File upload success
showSuccess('Upload Complete', 'Your file has been uploaded successfully.')

// File upload error
showError('Upload Failed', 'File size exceeds 10MB limit. Please choose a smaller file.')
```

## Technical Notes

### State Management
- Uses Vue 3 reactivity with `ref()` and `readonly()`
- Single global state prevents multiple dialogs
- Automatic cleanup on dialog close

### Styling
- Inherits Shadcn/ui dialog styling
- Success: Green checkmark icon
- Error: Red X icon
- Responsive design with max-width constraints

### Integration
- Automatically available in all components via AppLayout
- No additional setup required per component
- Works with Vue Router navigation