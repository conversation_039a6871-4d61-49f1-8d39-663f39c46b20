# Composable Best Practices - Reactive Props & Parameters

## Overview
When working with Vue 3 composables, proper handling of reactive data is crucial for maintaining reactivity and avoiding stale values. This guide covers best practices for passing reactive data to composables.

## Key Principle: Reactivity Preservation
The fundamental rule is to preserve reactivity when passing data between components and composables.

## Best Practice 1: Pass Callbacks for Reactive Props

### ❌ Wrong - Passing Raw Values
```ts
// This captures only the current value, not reactive updates
const result = useFormat(props.name) // Static snapshot
```

### ✅ Correct - Pass Callback Functions
```ts
// This maintains reactivity by re-evaluating on every access
const result = useFormat(() => props.name) // Always reactive
```

**Key Point**: `() => props.foo` remains reactive, whereas `props.foo` is just a snapshot at the time of call.

## Best Practice 2: Use MaybeRefOrGetter in Composables

### Flexible Parameter Types
Type your composable parameters as `MaybeRefOrGetter<T>` to accept multiple input types:

```ts
import { type MaybeRefOrGetter, toValue, computed } from 'vue'

function useName(name: MaybeRefOrGetter<string>) {
  const uppercase = computed(() => toValue(name).toUpperCase())
  return { uppercase }
}
```

### Usage Examples - All Equivalent and Reactive
```ts
// 1. Literal value
useName("matt")

// 2. Ref value
useName(ref("matt"))

// 3. Getter function (recommended for props)
useName(() => props.name)

// 4. Computed value
useName(computed(() => user.value.name))
```

## Implementation Pattern

### Complete Composable Example
```ts
import { type MaybeRefOrGetter, toValue, computed, watch } from 'vue'

function useFormatter(input: MaybeRefOrGetter<string>) {
  // Use toValue() to unwrap while maintaining reactivity
  const formatted = computed(() => {
    const value = toValue(input)
    return value.toLowerCase().trim()
  })
  
  // Watch reactive changes
  watch(() => toValue(input), (newValue) => {
    console.log('Input changed:', newValue)
  })
  
  return { formatted }
}
```

### Component Usage
```vue
<script setup lang="ts">
import { ref } from 'vue'

const props = defineProps<{
  userName: string
  isActive: boolean
}>()

const localName = ref('John')

// ✅ Correct - All maintain reactivity
const userFormatter = useFormatter(() => props.userName)
const localFormatter = useFormatter(() => localName.value)
const refFormatter = useFormatter(localName) // ref is automatically reactive
</script>
```

## Common Patterns

### 1. Conditional Reactive Values
```ts
function useConditionalValue(
  condition: MaybeRefOrGetter<boolean>,
  trueValue: MaybeRefOrGetter<string>,
  falseValue: MaybeRefOrGetter<string>
) {
  return computed(() => {
    return toValue(condition) ? toValue(trueValue) : toValue(falseValue)
  })
}

// Usage
const result = useConditionalValue(
  () => props.isActive,
  () => props.activeText,
  () => props.inactiveText
)
```

### 2. Array/Object Props
```ts
function useListProcessor(items: MaybeRefOrGetter<string[]>) {
  const processedItems = computed(() => {
    return toValue(items).map(item => item.toUpperCase())
  })
  
  return { processedItems }
}

// Usage
const processor = useListProcessor(() => props.items)
```

## Why This Matters

### Problem with Direct Prop Passing
```ts
// ❌ This breaks reactivity
const { result } = useMyComposable(props.value)

// When props.value changes, useMyComposable doesn't know about it
```

### Solution with Getter Functions
```ts
// ✅ This maintains reactivity
const { result } = useMyComposable(() => props.value)

// When props.value changes, the composable automatically receives the update
```

## Migration Guide

### Converting Existing Code
```ts
// Before (non-reactive)
function useOldWay(value: string) {
  return computed(() => value.toUpperCase())
}

// After (reactive)
function useNewWay(value: MaybeRefOrGetter<string>) {
  return computed(() => toValue(value).toUpperCase())
}

// Usage change
// Before: useOldWay(props.name)
// After: useNewWay(() => props.name)
```

## Performance Considerations

- `toValue()` is lightweight and optimized for reactive unwrapping
- Getter functions (`() => props.foo`) are efficient and don't create unnecessary watchers
- `computed()` automatically tracks dependencies from `toValue()` calls

## Summary Checklist

- ✅ Use `() => props.foo` instead of `props.foo` when passing to composables
- ✅ Type composable parameters as `MaybeRefOrGetter<T>`
- ✅ Use `toValue()` inside composables to unwrap values reactively
- ✅ Wrap reactive logic in `computed()` for automatic dependency tracking
- ✅ Test reactivity by changing prop values and verifying updates