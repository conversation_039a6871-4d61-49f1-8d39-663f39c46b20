{"mcpServers": {"context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"]}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "Figma-MCP": {"command": "npx", "args": ["-y", "figma-developer-mcp", "--figma-api-key=*********************************************", "--st<PERSON>"]}, "playwright": {"command": "npx", "args": ["@playwright/mcp@latest", "--isolated", "--storage-state=./storage.json"]}}}