# TON SDK Development Setup

This project includes automated scripts to set up development environment for TON SDK with public tunnel access.

## Quick Start

### Option 1: Auto Development (Recommended)
```bash
npm run dev
```
This automatically:
- ✅ Sets up localhost.run tunnel
- ✅ Starts your Vue.js development server
- ✅ Updates configuration files automatically
- ✅ Keeps both services running

### Option 2: Manual Scripts
```bash
./dev-with-tunnel.sh  # Same as npm run dev
```

### Option 3: Tunnel Only
```bash
npm run tunnel
# or
./setup-ton-dev.sh
```
Then in another terminal:
```bash
npm run dev:local  # Local development without tunnel
```

## What These Scripts Do

1. **Create SSH Tunnel**: Connects to localhost.run to expose your local server (port 5173) to the internet
2. **Extract Public URL**: Automatically parses the tunnel output to get your public HTTPS URL
3. **Update tonconnect-manifest.json**: Updates the manifest file with your public URL
4. **Update .env**: Sets `VITE_APP_URL` to your public URL
5. **Start Development**: Optionally starts the Vue.js development server

## Available Scripts

- `npm run dev` - Start development with tunnel (recommended)
- `npm run dev:local` - Start local development server only
- `npm run tunnel` - Set up tunnel only
- `npm run build` - Build for production
- `npm run preview` - Preview production build

## Files Updated

- `public/tonconnect-manifest.json` - TON Connect manifest with public URLs
- `.env` - Environment variables with `VITE_APP_URL`

## Manual Process (What the script automates)

Before these scripts, you had to:
1. Run `ssh -R 80:localhost:5173 localhost.run`
2. Copy the public URL from terminal output
3. Manually edit `public/tonconnect-manifest.json`
4. Manually edit `.env` file
5. Start development server separately

## Troubleshooting

### Tunnel fails to connect
- Check internet connection
- Ensure port 5173 is not in use
- Try running `ssh -R 80:localhost:5173 localhost.run` manually to see error messages

### Configuration files not updated
- Check if `public/` directory exists
- Ensure script has write permissions
- Verify the tunnel URL was extracted correctly

### Development server conflicts
- Stop any existing development servers on port 5173
- Use `pkill -f vite` to kill any stuck processes

## Stopping the Services

Press `Ctrl+C` in the terminal where the scripts are running. The cleanup functions will automatically:
- Terminate the tunnel connection
- Stop the development server
- Clean up any remaining processes

## Notes

- Keep the terminal open while developing to maintain the tunnel
- The public URL changes each time you restart the tunnel
- Configuration files are automatically updated each time you run the script
- Mobile testing is available through the public URL
