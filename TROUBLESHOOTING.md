# MINI_APP_URL Setup Troubleshooting

## ✅ The script functions work correctly!

I just tested and confirmed that the MINI_APP_URL update function works perfectly. It successfully updates both:
- App's `.env` file with `MINI_APP_URL`
- Bot's `.env` file with `MINI_APP_URL`

## 🔍 Why MINI_APP_URL might not be getting set:

### 1. **Tunnel needs to be established first**
The script only updates MINI_APP_URL **after** a successful tunnel connection. The process is:

```
1. Start tunnel → 2. Get URL → 3. Update MINI_APP_URL → 4. Restart bot
```

### 2. **Check if tunnel is working:**
```bash
# Run the script and look for this output:
./setup-ton-dev.sh

# You should see:
🔗 Starting localhost.run tunnel...
🔍 Waiting for tunnel URL...
✅ Found tunnel URL: https://xxxxxxx.lhr.life
📝 Updating app .env file...
✅ Updated .env
📝 Updating bot .env file...
✅ Updated ../bot/.env
🤖 Checking for running bot processes...
```

### 3. **Common issues:**

#### **Issue A: Tunnel connection fails**
```
❌ Failed to extract tunnel URL after 30 seconds
```
**Solution**: Check internet connection, try again

#### **Issue B: Script stops early**
If script exits before "Updating bot .env file", the tunnel didn't establish.

#### **Issue C: Bot .env file not found**
```
⚠️ Bot .env file not found, skipping bot configuration
```
**Solution**: Make sure you're running from the app directory

## 🧪 **Test the functions manually:**

```bash
# Quick test to verify bot .env update works:
cd app
echo "Testing..." && temp_file=$(mktemp) && grep -v "^MINI_APP_URL=" ../bot/.env > "$temp_file" && echo "MINI_APP_URL=https://test.lhr.life" >> "$temp_file" && echo "NEW CONTENT:" && cat "$temp_file" && rm "$temp_file"
```

## 🚀 **Complete setup process:**

1. **Start the tunnel script:**
   ```bash
   cd app
   ./setup-ton-dev.sh
   ```

2. **Keep terminal open** (tunnel needs to stay active)

3. **Check the logs** for successful updates:
   - `✅ Updated .env` (app's .env file)
   - `✅ Updated ../bot/.env` (bot's .env file)

4. **Verify the URLs were updated:**
   ```bash
   # Check app .env
   grep MINI_APP_URL .env

   # Check bot .env
   grep MINI_APP_URL ../bot/.env
   ```

## ❓ **Still not working?**

If you're still seeing "it still not set", please:

1. Run: `./setup-ton-dev.sh` and copy the complete output
2. Check: `grep MINI_APP_URL .env && grep MINI_APP_URL ../bot/.env`
3. Let me know at which step it's failing

The functions are working correctly - we just need to identify where in the process it's not completing!
