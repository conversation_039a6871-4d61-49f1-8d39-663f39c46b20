{"withdrawal": {"shiba": {"title": "Enter Your SHIB BEP-20 wallet", "description": "This amount will be sent to the SHIBA BEP-20 compatible wallet address"}, "ton": {"title": "Enter Your TON Wallet (Do Not Use Exchange Wallet)", "description": "This amount will be sent to the TON compatible wallet address.", "description2": "Withdrawals can take 1 to 72 hours to be processed."}, "walletAddress": {"required": "Wallet address is required"}, "amount": {"required": "Amount is required", "minimum": "Minimum amount is {minimumAmount} {token}", "insufficient": "Insufficient balance"}, "success": {"title": "<PERSON><PERSON><PERSON> Requested", "message": "Your withdrawal request for {amount} {token} has been submitted successfully and will be processed within 1-72 hours."}, "error": {"title": "Withdrawal Failed", "message": "Unable to process your withdrawal request. Please try again."}}, "boost": {"title": "Mining Power Boost", "description": "Here, you can boost your mining power For 30 Days. The more mining power you have, the more TON you can mine, leading to higher daily and monthly profits.", "monthly_profit": "Monthly Profit", "daily_profit": "Daily Profit", "amount": {"required": "Amount is required", "minimum": "Minimum amount is {minimumAmount} Ton", "minimum_text": "Minimum Amount {minimumAmount} Ton", "insufficient": "Insufficient balance"}, "minimum_notice": "Minimum Amount {minimumAmount} Ton", "success": {"title": "<PERSON><PERSON>chased", "message": "Your mining speed has been boosted to {speed} TH/s for 30 days using {amount} TON."}, "error": {"title": "Boost Failed", "message": "Unable to purchase boost. Please try again."}, "processing": "Processing...", "purchase_boost": "Boost", "preparing_payment": "Preparing Payment...", "sending_transaction": "Sending Transaction...", "completing_payment": "Completing Payment...", "payment_success": "Payment completed successfully!", "payment_failed": "Payment failed. Please try again.", "init_payment_failed": "Failed to initialize payment", "transaction_failed": "Transaction failed", "no_transaction": "No transaction available", "payment_not_completed": "Payment not completed", "complete_payment_failed": "Failed to complete payment"}, "claim": {"title": "Claim TON to wallet balance", "description": "Once claimed, the mined TON will be deducted from your mining balance and will be credited to your wallet balance.", "pending_balance": "Pending balance", "minimum_amount": "Minimum Amount {amount} Ton", "insufficient_warning": "You need to have at least {minimum} TON to claim.", "processing": "Processing...", "claim_button": "<PERSON><PERSON><PERSON>", "success": "Mining tokens claimed successfully! {amount} TON has been added to your wallet.", "failed": "Failed to claim mining tokens. Please try again.", "insufficient_tokens": "You need to have at least {minimum} pending tokens to claim"}, "starConversion": {"title": "Convert Stars to TON", "description": "Convert your Telegram Stars to TON tokens. The conversion will be processed instantly.", "starAmount": "Star Amount", "starAmountPlaceholder": "Enter number of stars", "estimatedTon": "Estimated TON", "conversionRate": "1000 Stars = 5 TON", "processing": "Converting...", "convertButton": "Convert Stars", "fetchingTon": "Calculating...", "amount": {"required": "Star amount is required", "minimum": "Minimum amount is {minimum} Stars", "insufficient": "Insufficient star balance"}, "success": {"title": "Conversion Successful", "message": "Successfully converted {starAmount} Stars to {tonAmount} TON!"}, "error": {"title": "Conversion Failed", "message": "Unable to convert stars. Please try again.", "cancelled": "Conversion cancelled."}}, "common": {"back": "Back"}, "home": {"claimButton": "<PERSON><PERSON><PERSON>", "boostButton": "Boost", "convertStarButton": "Convert star to TON", "convertStarButtonLoading": "Converting..."}, "wallet": {"address": "Address:", "selectWalletAddress": "Select wallet address", "disconnectCurrentAddress": "Disconnect current address", "copyCurrentAddress": "Copy current address"}, "transaction": {"status": {"completed": "Completed", "pending": "Pending", "failed": "Failed", "cancelled": "Cancelled"}, "table": {"headers": {"status": "Status", "date": "Date", "amount": "Amount", "description": "Description", "boost": "Boost"}, "noResults": "No results found."}}}