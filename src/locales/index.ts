import { createI18n } from 'vue-i18n'

// Import all locale files
import en from './en.json'
import es from './es.json'
import fa from './fa.json'
import hi from './hi.json'
import it from './it.json'
import ru from './ru.json'

// Supported locales
export const SUPPORTED_LOCALES = ['en', 'ru', 'hi', 'fa', 'it', 'es'] as const

export type SupportedLocale = typeof SUPPORTED_LOCALES[number]

// Messages object
const messages = {
  en,
  ru,
  hi,
  fa,
  it,
  es,
}

// Keep a reference to the i18n instance used by the app
let i18nInstance: ReturnType<typeof createI18n> | null = null

// Resolve incoming codes like "en-US" to a supported short code, fallback to 'en'
export function resolveLocale(code?: string): SupportedLocale {
  const lower = (code || 'en').toLowerCase()
  const short = lower.split('-')[0]
  const list = SUPPORTED_LOCALES as readonly string[]
  if (list.includes(lower)) return lower as SupportedLocale
  if (list.includes(short)) return short as SupportedLocale
  return 'en'
}

// Create i18n instance (called once in main.ts)
export function createAppI18n(locale: SupportedLocale = 'en') {
  const i18n = createI18n({
    locale,
    fallbackLocale: 'en',
    messages,
  })
  i18nInstance = i18n
  // reflect current language in the <html> tag
  if (typeof document !== 'undefined') document.documentElement.lang = locale
  return i18n
}

// Change app locale at runtime
export function setAppLocale(locale: SupportedLocale) {
  if (!i18nInstance) return
  // Support both legacy and composition modes
  // @ts-expect-error: accessing internal mode
  if (i18nInstance.mode === 'legacy') {
    // @ts-expect-error: legacy global
    i18nInstance.global.locale = locale
  }
  else {
    // @ts-expect-error: composer ref
    i18nInstance.global.locale.value = locale
  }
  if (typeof document !== 'undefined') document.documentElement.lang = locale
}

// Convenience: apply user language_code directly
export function applyUserLanguage(language_code?: string | null) {
  const locale = resolveLocale(language_code ?? undefined)
  setAppLocale(locale)
}

// Access the active i18n instance if needed
export function getI18nInstance() {
  return i18nInstance
}

// Export messages for external use
export { messages }
