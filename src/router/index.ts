import { createRouter, createWebHistory } from 'vue-router'
import Home from '../views/Home.vue'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home,
  },
  {
    path: '/rewards',
    name: 'Rewards',
    component: () => import('../views/Rewards.vue'),
  },
  {
    path: '/wallet',
    name: 'Wallet',
    component: () => import('../views/Wallet.vue'),
  },
  {
    path: '/top-users',
    name: 'TopUsers',
    component: () => import('../views/TopUsers.vue'),
  },
]

const router = createRouter({
  history: createWebHistory(),
  routes,
})

export default router
