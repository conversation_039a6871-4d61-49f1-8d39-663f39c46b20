<script setup lang="ts">
import { Info, Search } from 'lucide-vue-next'
import { computed, ref } from 'vue'
import LeaderboardRow from '@/components/leaderboard/LeaderboardRow.vue'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'

interface User {
  id: string
  name: string
  handle: string
  speedThs: number
  ton: number
  trend: 'up' | 'down' | 'flat'
  avatarUrl?: string
}

const speedUsers: User[] = [
  { id: '31w93pxyehbc', name: 'Subhon', handle: '#31w93pxyehbc', speedThs: 3.22, ton: 4, trend: 'up' },
  { id: '3ye3gyld6mf8', name: 'User 3ye3gyld6mf8', handle: '#3ye3gyld6mf8', speedThs: 2.42, ton: 3, trend: 'up' },
  { id: 'gfgj3r8qheb6', name: 'dark knight', handle: '#gfgj3r8qheb6', speedThs: 2.42, ton: 3, trend: 'flat' },
  { id: '18fnn3vf35ad', name: 'S Das', handle: '#18fnn3vf35ad', speedThs: 2.42, ton: 3, trend: 'flat' },
  { id: 'mlv7gux7wg9e', name: 'Crowley', handle: '#mlv7gux7wg9e', speedThs: 2.42, ton: 3, trend: 'flat' },
  { id: '9e6ge2a2ewpq', name: 'vamsi krishna', handle: '#9e6ge2a2ewpq', speedThs: 2.42, ton: 3, trend: 'down' },
  { id: '8g1n9u9fmw6', name: 'X Evans Felix', handle: '#8g1n9u9fmw6', speedThs: 2.42, ton: 3, trend: 'flat' },
  { id: '0tau4bkl89yl', name: 'Amir', handle: '#0tau4bkl89yl', speedThs: 2.42, ton: 3, trend: 'flat' },
  { id: 'fa2yhb7g0f9t5', name: 'Draen NeoHash RICHYescoiner Jerkovi', handle: '#fa2yhb7g0f9t5', speedThs: 2.42, ton: 3, trend: 'up' },
  { id: 'wonnhkdzpbbx', name: 'Mvc003', handle: '#wonnhkdzpbbx', speedThs: 2.42, ton: 3, trend: 'flat' },
]

const inviteUsers: User[] = speedUsers
  .map(u => ({ ...u, ton: Math.max(1, Math.round(u.ton + (Math.random() > 0.5 ? 1 : -1))) }))
  .sort((a, b) => b.ton - a.ton)

const tab = ref<'speed' | 'invites'>('speed')
const q = ref('')

const dataset = computed(() => (tab.value === 'speed' ? speedUsers : inviteUsers))

const filtered = computed(() => {
  if (!q.value.trim())
    return dataset.value
  const k = q.value.toLowerCase()
  return dataset.value.filter(u =>
    u.name.toLowerCase().includes(k) || u.handle.toLowerCase().includes(k) || `${u.speedThs}`.includes(k) || `${u.ton}`.includes(k),
  )
})
</script>

<template>
  <div class="mx-auto max-w-4xl">
    <header class="mb-4 md:mb-6">
      <h1 class="text-2xl font-semibold tracking-tight md:text-3xl">
        Top Users
      </h1>
      <p class="text-sm text-muted-foreground mt-1">
        Explore the leaderboard by mining speed or invitations. Use search to quickly find a user.
      </p>
    </header>

    <Card class="border-muted/40">
      <div class="sticky top-0 z-10 bg-card/80 backdrop-blur supports-[backdrop-filter]:bg-card/60">
        <div class="flex flex-col gap-3 p-3 md:p-4">
          <div class="flex items-center justify-between gap-3">
            <Tabs v-model="tab" class="w-full">
              <TabsList class="w-full sm:w-auto">
                <TabsTrigger value="speed" class="w-full sm:w-auto">
                  Speed
                </TabsTrigger>
                <TabsTrigger value="invites" class="w-full sm:w-auto">
                  Invitations
                </TabsTrigger>
              </TabsList>
            </Tabs>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger as-child>
                  <Button variant="ghost" size="icon" class="shrink-0">
                    <Info class="h-4 w-4" aria-hidden="true" />
                    <span class="sr-only">About leaderboard</span>
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="left" align="center" class="max-w-xs">
                  <p class="text-xs">
                    Speed is measured in TH/s. Invitations count indicates verified invites claimed as Ton rewards.
                  </p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>

          <div class="relative">
            <Search class="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" aria-hidden="true" />
            <Input v-model="q" placeholder="Search by name, handle, or stat..." class="pl-8" aria-label="Search users" />
          </div>
        </div>
      </div>

      <CardContent class="p-0">
        <Tabs v-model="tab">
          <TabsContent value="speed" class="m-0">
            <ScrollArea class="max-h-[70vh]">
              <ul class="p-3 md:p-4 space-y-2">
                <li v-for="(u, idx) in filtered" :key="u.id">
                  <LeaderboardRow :user="u" :rank="idx + 1" :mode="tab" />
                </li>
              </ul>
            </ScrollArea>
          </TabsContent>
          <TabsContent value="invites" class="m-0">
            <ScrollArea class="max-h-[70vh]">
              <ul class="p-3 md:p-4 space-y-2">
                <li v-for="(u, idx) in filtered" :key="u.id">
                  <LeaderboardRow :user="u" :rank="idx + 1" :mode="tab" />
                </li>
              </ul>
            </ScrollArea>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  </div>
</template>
