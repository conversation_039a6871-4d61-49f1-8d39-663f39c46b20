<script setup lang="ts">
import type { Txn, TxnStatus, TxnType } from '@/lib/transactions'
import { onBeforeUnmount, onMounted, ref } from 'vue'
import { toast } from 'vue-sonner'
import { Separator } from '@/components/ui/separator'
import { Toaster } from '@/components/ui/sonner'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import AddressBar from '@/components/wallet/AddressBar.vue'
import TxnBonusTable from '@/components/wallet/TxnBonusTable.vue'
import TxnDepositTable from '@/components/wallet/TxnDepositTable.vue'
import TxnWithdrawalsTable from '@/components/wallet/TxnWithdrawalsTable.vue'

import { sampleTransactions } from '@/lib/transactions'

// Data
const transactions = ref<Txn[]>(sampleTransactions())
let interval: number | undefined

onMounted(() => {
  interval = window.setInterval(() => {
    transactions.value = transactions.value.map(t =>
      t.status === 'pending' && Date.now() - t.createdAt > 5000 ? { ...t, status: 'completed' as TxnStatus } : t,
    )
  }, 2000)
})

onBeforeUnmount(() => {
  if (interval)
    clearInterval(interval)
})

// UI state
const tab = ref<TxnType | 'all'>('deposit')
</script>

<template>
  <div class="min-h-screen w-full">
    <main>
      <div class="mb-4">
        <h1 class="text-2xl font-semibold tracking-tight md:text-3xl">
          Wallet
        </h1>
        <p class="text-sm text-slate-400">
          Manage deposits, withdrawals, and bonus rewards
        </p>
      </div>

      <div class="w-full">
        <AddressBar
          :addresses="['UQBFSgckrQ2hX3pBq9nJ9p8QG9k4rPPCJFHTE', 'UQC6AvVxM5zYt4i1oNsWxY2z3pZ7hZPp7ZPE']"
          @copied="toast('Address copied', { description: 'Wallet address copied to clipboard.' })"
        />
      </div>

      <Separator class="bg-slate-800" />

      <Tabs v-model="tab" class="mt-6">
        <TabsList class="grid w-full grid-cols-3 bg-[#12161b] text-slate-100">
          <TabsTrigger value="deposit" class="data-[state=active]:bg-[#1c232b]">
            Deposits
          </TabsTrigger>
          <TabsTrigger value="withdraw" class="data-[state=active]:bg-[#1c232b]">
            Withdrawals
          </TabsTrigger>
          <TabsTrigger value="bonus" class="data-[state=active]:bg-[#1c232b]">
            Bonuses
          </TabsTrigger>
        </TabsList>

        <TabsContent value="deposit" class="mt-4">
          <Suspense>
            <TxnDepositTable />
            <template #fallback>
              <div class="flex items-center justify-center h-full">
                <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-slate-900" />
              </div>
            </template>
          </Suspense>
        </TabsContent>
        <TabsContent value="withdraw" class="mt-4">
          <Suspense>
            <TxnWithdrawalsTable />
            <template #fallback>
              <div class="flex items-center justify-center h-full">
                <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-slate-900" />
              </div>
            </template>
          </Suspense>
        </TabsContent>
        <TabsContent value="bonus" class="mt-4">
          <Suspense>
            <TxnBonusTable />
            <template #fallback>
              <div class="flex items-center justify-center h-full">
                <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-slate-900" />
              </div>
            </template>
          </Suspense>
        </TabsContent>
      </Tabs>
    </main>
    <Toaster position="top-right" rich-colors />
  </div>
</template>
