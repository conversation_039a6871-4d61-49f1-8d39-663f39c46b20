import { getI18nInstance } from '@/locales'
import { useUserStore } from '@/stores/userStore'

export function queryStringToObject(queryString: string): Record<string, string> {
  const params = new URLSearchParams(queryString)
  const result: Record<string, string> = {}
  for (const [key, value] of params.entries()) {
    result[key] = value
  }
  return result
}

/**
 * Gets the current user's locale from i18n instance or user store
 * Falls back to browser locale or 'en-US' if not available
 */
function getCurrentLocale(): string {
  try {
    // Try to get locale from i18n instance first
    const i18n = getI18nInstance()
    if (i18n) {
      // @ts-expect-error: accessing internal locale
      const currentLocale = i18n.global.locale?.value || i18n.global.locale
      if (currentLocale) {
        // Convert short codes to full locale codes for better formatting
        const localeMap: Record<string, string> = {
          en: 'en-US',
          ru: 'ru-RU',
          hi: 'hi-IN',
          fa: 'fa-IR',
          it: 'it-IT',
          es: 'es-ES',
        }
        return localeMap[currentLocale] || currentLocale
      }
    }
  }
  catch {
    // If i18n is not available, continue to fallback
  }

  try {
    // Try to get from user store as fallback
    const userStore = useUserStore()
    if (userStore.user?.language_code) {
      return userStore.user.language_code
    }
  }
  catch {
    // If user store is not available, continue to fallback
  }

  // Final fallback to browser locale or en-US
  return navigator.language || 'en-US'
}

/**
 * Formats a Laravel backend datetime string into a user-friendly format
 * Uses the user's current locale for localized date formatting
 *
 * @param date - ISO 8601 datetime string from Laravel backend (e.g., "2024-01-15T10:30:00Z")
 * @returns Formatted date string localized to user's language (e.g., "Jan 15, 2024 at 10:30 AM")
 */
export function formatDate(date: string): string {
  try {
    const dateObj = new Date(date)

    // Check if the date is valid
    if (Number.isNaN(dateObj.getTime())) {
      return 'Invalid date'
    }

    // Get the user's current locale
    const locale = getCurrentLocale()

    // Format options for a user-friendly display
    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    }

    // Use the user's locale for formatting
    const formatted = dateObj.toLocaleDateString(locale, options)

    // For English locales, replace comma with "at" for better readability
    if (locale.startsWith('en')) {
      return formatted.replace(',', ' at')
    }

    return formatted
  }
  catch {
    // Return a fallback if parsing fails
    return 'Invalid date'
  }
}

export function debounce<T extends (...args: any[]) => void>(fn: T, delay: number) {
  let timeoutId: number

  return function (this: any, ...args: any[]) {
    clearTimeout(timeoutId)
    timeoutId = window.setTimeout(() => fn.apply(this, args), delay)
  }
}

interface RetryOptions {
  retries?: number
  delay?: number
  exponentialBackoff?: boolean
  maxDelay?: number
  onRetry?: (error: any, attempt: number) => void
}

export async function retryAsync(func: () => Promise<any>, options: RetryOptions = {}) {
  const {
    retries = 3,
    delay = 1000,
    exponentialBackoff = false,
    maxDelay = 10000,
    onRetry = null,
  } = options

  let attempts = 0
  let currentDelay = delay
  let lastError

  while (attempts < retries) {
    try {
      const result = await func()
      return result
    }
    catch (error) {
      lastError = error
      attempts++

      if (attempts < retries) {
        if (onRetry) {
          onRetry(error, attempts)
        }

        await new Promise(resolve => setTimeout(resolve, currentDelay))

        if (exponentialBackoff) {
          currentDelay = Math.min(currentDelay * 2, maxDelay)
        }
      }
    }
  }

  throw lastError
}

export function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms))
}
