import axios from 'axios'
import CryptoJ<PERSON> from 'crypto-js'

// Get the secret key from environment variables
const SECRET_KEY = import.meta.env.VITE_API_SECRET_KEY || ''

// Validate that the secret key exists
if (!SECRET_KEY) {
  console.error('API Secret Key is missing. Make sure VITE_API_SECRET_KEY is set in your .env file.')
}

/**
 * Creates a signature for the given data using SHA-256
 * @param data The data to sign
 * @param timestamp Timestamp in seconds
 * @returns The hexadecimal signature
 */
export function createSignature(data: Record<string, any>, timestamp: number): string {
  // Sort keys to ensure consistent order for signature
  const sortedData = Object.keys(data).sort().reduce(
    (obj, key) => {
      obj[key] = data[key]
      return obj
    },
    {} as Record<string, any>,
  )

  // Create a string representation of the data
  const dataString = JSON.stringify(sortedData)

  // Combine data, timestamp, and secret key
  const signatureBase = `${dataString}|${timestamp}|${SECRET_KEY}`

  // Create SHA-256 hash using CryptoJS (browser-compatible)
  return CryptoJS.SHA256(signatureBase).toString(CryptoJS.enc.Hex)
}

/**
 * API client that automatically adds signature to requests
 */
export const api = {
  /**
   * Make a signed GET request
   * @param url The endpoint URL
   * @param params Query parameters
   * @param options Additional axios options
   */
  async get<T>(url: string, params?: Record<string, any>, options?: any): Promise<T> {
    const timestamp = Math.floor(Date.now() / 1000)
    const dataToSign = params || {}
    const signature = createSignature(dataToSign, timestamp)

    const response = await axios.get(url, {
      params,
      ...options,
      headers: {
        ...options?.headers,
        'X-Signature': signature,
        'X-Timestamp': timestamp.toString(),
      },
    })

    return response.data
  },

  /**
   * Make a signed POST request
   * @param url The endpoint URL
   * @param data Request body data
   * @param options Additional axios options
   */
  async post<T>(url: string, data: Record<string, any>, options?: any): Promise<T> {
    const timestamp = Math.floor(Date.now() / 1000)
    const signature = createSignature(data, timestamp)

    const response = await axios.post(url, data, {
      ...options,
      headers: {
        ...options?.headers,
        'Content-Type': 'application/json',
        'X-Signature': signature,
        'X-Timestamp': timestamp.toString(),
      },
    })

    return response.data
  },

  /**
   * Make a signed PUT request
   * @param url The endpoint URL
   * @param data Request body data
   * @param options Additional axios options
   */
  async put<T>(url: string, data: Record<string, any>, options?: any): Promise<T> {
    const timestamp = Math.floor(Date.now() / 1000)
    const signature = createSignature(data, timestamp)

    const response = await axios.put(url, data, {
      ...options,
      headers: {
        ...options?.headers,
        'Content-Type': 'application/json',
        'X-Signature': signature,
        'X-Timestamp': timestamp.toString(),
      },
    })

    return response.data
  },

  /**
   * Make a signed DELETE request
   * @param url The endpoint URL
   * @param params Query parameters
   * @param options Additional axios options
   */
  async delete<T>(url: string, params?: Record<string, any>, options?: any): Promise<T> {
    const timestamp = Math.floor(Date.now() / 1000)
    const dataToSign = params || {}
    const signature = createSignature(dataToSign, timestamp)

    const response = await axios.delete(url, {
      params,
      ...options,
      headers: {
        ...options?.headers,
        'X-Signature': signature,
        'X-Timestamp': timestamp.toString(),
      },
    })

    return response.data
  },
}
