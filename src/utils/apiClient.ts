import type { AxiosRequestConfig, AxiosResponse, Method } from 'axios'
import axios from 'axios'
import { useUserStore } from '@/stores/userStore'
import { createSignature } from './apiUtils'

// API client options interface, similar to fetch's RequestInit
export interface ApiClientOptions {
  method?: Method
  headers?: Record<string, string>
  params?: Record<string, any>
  data?: any
  timeout?: number
  withCredentials?: boolean
  responseType?: 'json' | 'text' | 'blob' | 'arraybuffer'
  // Add other options as needed
}

/**
 * Creates an API client for making signed requests
 * @param baseURL The base URL for the API (optional)
 * @returns An API client object
 */
export function createApiClient() {
  const client = {
    /**
     * Makes a request to the API with automatic signing
     * @param url The URL to make the request to
     * @param options The request options
     * @returns A promise that resolves to the response data
     */
    async request<T = any>(url: string, options: ApiClientOptions = {}): Promise<T> {
      const {
        method = 'GET',
        headers = {},
        params,
        data,
        ...restOptions
      } = options

      // Current timestamp in seconds
      const timestamp = Math.floor(Date.now() / 1000)

      // Data to sign: Either request body for POST/PUT/PATCH, or query params for GET/DELETE
      const dataToSign = method === 'GET' || method === 'DELETE'
        ? (params || {})
        : (data || {})

      // Create signature
      const signature = createSignature(dataToSign, timestamp)

      const userStore = useUserStore()

      console.log('new client', userStore.accessToken)

      if (userStore.accessToken) {
        headers.Authorization = `Bearer ${userStore.accessToken}`
      }

      delete headers['X-Signature']
      delete headers['X-Timestamp']
      // Configure request
      const config: AxiosRequestConfig = {
        url,
        method,
        baseURL: import.meta.env.VITE_API_URL,
        headers: {
          'Content-Type': 'application/json',
          'X-Signature': signature,
          'X-Timestamp': timestamp.toString(),
          ...headers,
        },
        params,
        data,
        ...restOptions,
      }

      try {
        const response: AxiosResponse<T> = await axios(config)
        return response.data
      }
      catch (error: any) {
        // Enhance error with more details if available
        if (error.response) {
          // The request was made and the server responded with a status code
          // that falls out of the range of 2xx
          console.error('API Error:', {
            status: error.response.status,
            data: error.response.data,
          })
        }

        else if (error.request) {
          // The request was made but no response was received
          console.error('API Error: No response received', error.request)
        }
        else {
          // Something happened in setting up the request that triggered an Error
          console.error('API Error:', error.message)
        }

        throw error
      }
    },

    // Convenience methods for common HTTP verbs
    async get<T = any>(url: string, options: Omit<ApiClientOptions, 'method' | 'data'> = {}): Promise<T> {
      return this.request<T>(url, { ...options, method: 'GET' })
    },

    async post<T = any>(url: string, data?: any, options: Omit<ApiClientOptions, 'method' | 'data'> = {}): Promise<T> {
      return this.request<T>(url, { ...options, method: 'POST', data })
    },

    async put<T = any>(url: string, data?: any, options: Omit<ApiClientOptions, 'method' | 'data'> = {}): Promise<T> {
      return this.request<T>(url, { ...options, method: 'PUT', data })
    },

    async patch<T = any>(url: string, data?: any, options: Omit<ApiClientOptions, 'method' | 'data'> = {}): Promise<T> {
      return this.request<T>(url, { ...options, method: 'PATCH', data })
    },

    async delete<T = any>(url: string, options: Omit<ApiClientOptions, 'method'> = {}): Promise<T> {
      return this.request<T>(url, { ...options, method: 'DELETE' })
    },
  }

  return client
}

// Create a default API client instance
const apiClient = createApiClient()

export default apiClient
