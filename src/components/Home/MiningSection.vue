<script setup lang="ts">
import { LucideAlertCircle } from 'lucide-vue-next'
import { storeToRefs } from 'pinia'
import { computed, onMounted, watch } from 'vue'
import { useCountUp } from '@/composables/useCountUp'
import { useUserStore } from '@/stores/userStore'
import Spinner from '../shared/Spinner/Spinner.vue'

const { user } = storeToRefs(useUserStore())

// User's TON balance as start value for count-up animation
const pendingTokens = computed(() => {
  if (!user.value)
    return 0
  return Number.parseFloat(user.value.pending_token) || 0
})

// Mining speed in TON per second
const miningSpeed = computed(() => {
  if (!user.value)
    return 0
  return user.value.speed / 1_000_000_000
})

// Spinner duration based on mining speed
const spinnerDuration = computed(() => {
  if (!user.value || user.value.speed === 0)
    return 6 // Default max duration

  const speedGH = Number(user.value.speed / 1_000_000) // Convert to GH/s

  // Linear inverse relationship with proper scale:
  // speed 0 GH/s -> 6 seconds
  // speed 10000 GH/s -> 1 second
  // Formula: duration = 6 - (speed * 5 / 10000)
  const duration = Math.max(1, 6 - Number((speedGH * 100000 * 5) / 10000))

  return Number(duration.toFixed(1))
})

// Count-up animation for tokens - continuous increment from TON balance
// Use very small intervals for ultra-smooth animation (50ms intervals)
const tokenCounter = useCountUp(0, computed(() => ({
  continuous: true,
  increment: miningSpeed.value / 20, // 50ms tick: coinsPerSecond * 0.05 (matches backend)
  interval: 50, // Update every 50ms for faster, smoother animation
  decimals: 10,
  separator: ',',
  useGrouping: true,
})))

// Watch for user changes to restart mining
watch(user, () => {
  if (user.value) {
    // Reset to TON balance and restart continuous mining
    tokenCounter.setValue(pendingTokens.value)
    tokenCounter.startAnimation()
  }
}, { deep: true })

// Watch for mining speed changes to restart animation
watch(miningSpeed, () => {
  if (user.value) {
    // Restart with new speed
    tokenCounter.startAnimation()
  }
})

onMounted(() => {
  if (user.value) {
    // Set initial value to TON balance before starting animation
    tokenCounter.setValue(pendingTokens.value)

    // Start continuous mining animation
    setTimeout(() => {
      tokenCounter.startAnimation()
    }, 500)
  }
})

function getMiningSpeedText() {
  if (!user.value || user.value.speed === 0) {
    return '0 GH/s'
  }

  const speedValue = Number(user.value.speed)

  // Convert to appropriate unit
  if (speedValue >= 1000) {
    const phSpeed = speedValue / 1000
    return `${phSpeed.toFixed(2)} PH/s`
  }
  else if (speedValue >= 100) {
    const thSpeed = speedValue / 100
    return `${thSpeed.toFixed(2)} TH/s`
  }
  else {
    return `${speedValue.toFixed(2)} GH/s`
  }
}
</script>

<template>
  <section class="space-y-6">
    <!-- Mining Animation -->
    <div class="flex justify-center items-center">
      <Spinner size="xl" :duration="spinnerDuration" class="w-2/3" />
    </div>

    <!-- Mining Stats -->
    <div class="space-y-4">
      <!-- Current Balance -->
      <div class="text-center">
        <div class="text-3xl font-bold">
          {{ tokenCounter.formattedValue }} TON
        </div>
      </div>

      <!-- Mining Speed -->
      <div class="flex items-center gap-1 justify-center">
        <LucideAlertCircle class="w-4 h-4 cursor-pointer" />
        <div class="font-semibold">
          {{ getMiningSpeedText() }} ⚡️
        </div>
      </div>
    </div>
  </section>
</template>
