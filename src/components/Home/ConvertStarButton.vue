<script setup lang="ts">
import { ArrowRight, Star } from 'lucide-vue-next'
import { reactive } from 'vue'
import { useI18n } from 'vue-i18n'
import StarConversionDialog from '@/components/shared/Dialog/StarConversionDialog.vue'
import { Button } from '@/components/ui/button'

// Use i18n for translations
const { t } = useI18n()

// Dialog state management
const dialogState = reactive({
  open: false,
})

function handleClick() {
  dialogState.open = true
}

function handleConversionSuccess(_data: { starAmount: number, tonAmount: number }) {
  // Handle successful conversion
  // You can add additional logic here like refreshing user balance
  // Example: refresh user store, update UI, etc.
}
</script>

<template>
  <div>
    <Button
      class="w-full bg-gradient-to-r from-yellow-500 to-blue-500 hover:from-yellow-600 hover:to-blue-600 text-white py-4 rounded-xl font-medium text-lg transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
      @click="handleClick"
    >
      <Star class="w-5 h-5 mr-2 fill-current" />
      {{ t('home.convertStarButton') }}
      <ArrowRight class="w-5 h-5 ml-2" />
    </Button>

    <Teleport to="body">
      <StarConversionDialog
        v-model:open="dialogState.open"
        @success="handleConversionSuccess"
      />
    </Teleport>
  </div>
</template>
