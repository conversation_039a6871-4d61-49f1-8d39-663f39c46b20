<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { ref, watchEffect } from 'vue'
import { useI18n } from 'vue-i18n'
import BoosterDialog from '@/components/shared/Dialog/BoosterDialog.vue'
import { Button } from '@/components/ui/button'
import { useGlobalDialog } from '@/composables/useGlobalDialog'
import { useUserStore } from '@/stores/userStore'

const { user } = storeToRefs(useUserStore())
const { t } = useI18n()
const { showSuccess, dialogState } = useGlobalDialog()

const isBoostDialogOpen = ref(false)
const waitingForBoostSuccessDialogClose = ref(false)

// Watch for when the global dialog closes after showing boost success
watchEffect(() => {
  if (waitingForBoostSuccessDialogClose.value && !dialogState.value.isOpen) {
    // Dialog was closed, reload the page
    waitingForBoostSuccessDialogClose.value = false
    window.location.reload()
  }
})

function openBoostDialog() {
  isBoostDialogOpen.value = true
}

function handleBoostSuccess(data: { newSpeed: number, expiresAt: string }) {
  // Close the boost dialog first
  isBoostDialogOpen.value = false

  // Show success dialog with translated messages
  const title = t('boost.success.title')
  const message = t('boost.success.message', {
    speed: data.newSpeed.toFixed(2),
    amount: 'N/A', // We don't have the amount in the data, but it's in the translation
  })

  // Set flag to indicate we're waiting for this specific dialog to close
  waitingForBoostSuccessDialogClose.value = true

  // Show the success dialog
  showSuccess(title, message)

  // The watchEffect above will handle reloading the page when the dialog closes
}
</script>

<template>
  <div>
    <Button
      class="w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700"
      @click="openBoostDialog"
    >
      <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
      </svg>
      {{ t('boost.title') }}
    </Button>

    <BoosterDialog
      v-model:open="isBoostDialogOpen"
      :current-speed="user?.speed || 0"
      :balance="parseFloat(user?.balance_ton || '0')"
      @success="handleBoostSuccess"
    />
  </div>
</template>
