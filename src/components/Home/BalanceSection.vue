<script setup lang="ts">
import type { Token } from '@/types'
import { storeToRefs } from 'pinia'
import { reactive } from 'vue'
import WithdrawalDialog from '@/components/shared/Dialog/WithdrawalDialog.vue'
import { useUserStore } from '@/stores/userStore'
import BalanceCard from './BalanceCard.vue'

const { user } = storeToRefs(useUserStore())

const withdrawalDialogState = reactive<{
  open: boolean
  balance: number
  token: Token
}>({
  open: false,
  balance: 0,
  token: 'shiba',
})

function openWithdrawModal(token: Token, balance: number) {
  withdrawalDialogState.open = true
  withdrawalDialogState.token = token
  withdrawalDialogState.balance = balance
}
</script>

<template>
  <section class="space-y-2">
    <BalanceCard :balance="Number(user?.balance_ton).toFixed(6)" token="ton" @open="openWithdrawModal('ton', Number(user?.balance_ton || 0))" />
    <BalanceCard :balance="Number(user?.balance_token).toFixed(0)" token="shiba" @open="openWithdrawModal('shiba', Number(user?.balance_token || 0))" />

    <Teleport to="body">
      <WithdrawalDialog
        v-model:open="withdrawalDialogState.open"
        :balance="withdrawalDialogState.balance"
        :token="withdrawalDialogState.token"
      />
    </Teleport>
  </section>
</template>
