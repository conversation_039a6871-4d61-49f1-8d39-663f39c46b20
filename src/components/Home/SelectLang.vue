<script setup lang="ts">
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

interface Language {
  code: string
  name: string
  countryCode: string
}

const languages: Language[] = [
  { code: 'en', name: 'English', countryCode: 'us' },
  { code: 'ru', name: 'Russian', countryCode: 'ru' },
  { code: 'hi', name: 'Hindi', countryCode: 'in' },
  { code: 'fa', name: 'Farsi', countryCode: 'ir' },
  { code: 'it', name: 'Italian', countryCode: 'it' },
  { code: 'es', name: 'Spanish', countryCode: 'es' },
]

const { locale } = useI18n({ useScope: 'global' })

const selectedLanguage = computed(() => {
  return languages.find(lang => lang.code === locale.value) || languages[0]
})

function selectLanguage(language: Language) {
  locale.value = language.code
}

function getFlagUrl(countryCode: string) {
  return `https://raw.githubusercontent.com/hampusborgos/country-flags/refs/heads/main/svg/${countryCode}.svg`
}
</script>

<template>
  <DropdownMenu>
    <DropdownMenuTrigger
      class="flex items-center gap-2 px-3 py-2 bg-gray-800 hover:bg-gray-700 rounded-lg text-white transition-colors outline-none"
    >
      <img
        :src="getFlagUrl(selectedLanguage.countryCode)"
        :alt="selectedLanguage.name"
        class="w-5 h-4 object-cover rounded-sm"
      >
      <span class="text-sm">{{ selectedLanguage.code.toUpperCase() }}</span>
      <svg
        class="w-4 h-4 transition-transform"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M19 9l-7 7-7-7"
        />
      </svg>
    </DropdownMenuTrigger>

    <DropdownMenuContent class="bg-gray-800 border-gray-700 min-w-36">
      <DropdownMenuItem
        v-for="language in languages"
        :key="language.code"
        class="flex items-center gap-3 px-3 py-2 text-sm text-white hover:bg-gray-700 transition-colors cursor-pointer"
        @click="selectLanguage(language)"
      >
        <img
          :src="getFlagUrl(language.countryCode)"
          :alt="language.name"
          class="w-5 h-4 object-cover rounded-sm"
        >
        <span>{{ language.name }}</span>
      </DropdownMenuItem>
    </DropdownMenuContent>
  </DropdownMenu>
</template>
