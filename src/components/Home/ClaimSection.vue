<script setup lang="ts">
import { DatabaseIcon, LucideZap } from 'lucide-vue-next'
import { storeToRefs } from 'pinia'
import { reactive } from 'vue'
import { useI18n } from 'vue-i18n'
import BoosterDialog from '@/components/shared/Dialog/BoosterDialog.vue'
import ClaimedDialog from '@/components/shared/Dialog/ClaimedDialog.vue'
import { Button } from '@/components/ui/button'
import { useUserStore } from '@/stores/userStore'

const { user } = storeToRefs(useUserStore())
const { t } = useI18n()

const boosterDialogState = reactive({
  open: false,
})

const claimedDialogState = reactive({
  open: false,
})

function handleClaim() {
  claimedDialogState.open = true
}

function handleBoost() {
  boosterDialogState.open = true
}

function handleClaimSuccess() {

}
</script>

<template>
  <section class="space-y-4">
    <div class="grid grid-cols-2 gap-3">
      <Button
        class="w-full text-lg"
        size="lg"
        @click="handleClaim"
      >
        <DatabaseIcon class="size-5" />
        {{ t('home.claimButton') }}
      </Button>

      <Button
        class="w-full text-lg"
        size="lg"
        @click="handleBoost"
      >
        <LucideZap class="size-5" />
        {{ t('home.boostButton') }}
      </Button>
    </div>

    <Teleport to="body">
      <BoosterDialog
        v-model:open="boosterDialogState.open"
        :current-speed="user?.speed || 0"
        :balance="parseFloat(user?.balance_ton || '0')"
      />

      <ClaimedDialog
        v-model:open="claimedDialogState.open"
        :pending-tokens="parseFloat(user?.pending_token || '0')"
        :minimum-amount="1.0"
        @success="handleClaimSuccess"
      />
    </Teleport>
  </section>
</template>
