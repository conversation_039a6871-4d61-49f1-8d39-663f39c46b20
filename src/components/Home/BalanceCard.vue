<script setup lang="ts">
import { Wallet2Icon } from 'lucide-vue-next'
import IconShiba from '@/components/shared/Icon/IconShiba.vue'
import IconTon from '@/components/shared/Icon/IconTon.vue'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'

interface Props {
  balance?: string
  token?: string
}

withDefaults(defineProps<Props>(), {
  balance: '0',
  token: 'ton',
})

const emit = defineEmits(['open'])

function open() {
  emit('open')
}
</script>

<template>
  <Card class="py-0 border-none">
    <CardContent class="px-4 py-2">
      <div class="flex items-center justify-between">
        <!-- Left side: Avatar and Balance -->
        <div class="flex items-center gap-3">
          <IconTon v-if="token === 'ton'" class="w-8 h-8" />
          <IconShiba v-else class="w-8 h-8" />

          <div class="flex flex-col">
            <span class="text-sm text-muted">Balance</span>
            <span class="font-semibold">{{ balance }} {{ token.toUpperCase() }}</span>
          </div>
        </div>

        <!-- Right side: Copy Button -->
        <Button
          size="sm"
          @click="open"
        >
          <Wallet2Icon class="w-5 h-5" />
        </Button>
      </div>
    </CardContent>
  </Card>
</template>
