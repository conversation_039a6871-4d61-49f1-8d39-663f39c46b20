<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { useUserStore } from '@/stores/userStore'

const { user } = storeToRefs(useUserStore())
</script>

<template>
  <div v-if="user" class="flex items-center gap-2">
    <img :src="user.avatar_url || ''" :alt="user.username" class="w-10 h-10 rounded-full border border-gray-500">
    <div>
      <div class="font-semibold">
        {{ user.name }}
      </div>
      <div class="text-xs text-muted">
        {{ user.tele_id }}
      </div>
    </div>
  </div>
</template>
