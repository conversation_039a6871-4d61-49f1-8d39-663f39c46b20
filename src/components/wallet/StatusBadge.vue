<script setup lang="ts">
import type { TxnStatus } from '@/lib/transactions'
import { CheckCircle2, Clock3, XCircle } from 'lucide-vue-next'
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { Badge } from '@/components/ui/badge'

const props = defineProps<{ status: TxnStatus }>()

const { t } = useI18n()

const meta = computed(() => {
  const map: Record<TxnStatus, { label: string, icon: any, className: string }> = {
    completed: {
      label: t('transaction.status.completed'),
      icon: CheckCircle2,
      className: 'bg-emerald-600/20 text-emerald-400 border-emerald-700/40',
    },
    pending: {
      label: t('transaction.status.pending'),
      icon: Clock3,
      className: 'bg-amber-500/15 text-amber-400 border-amber-700/40',
    },
    failed: {
      label: t('transaction.status.failed'),
      icon: XCircle,
      className: 'bg-rose-500/15 text-rose-400 border-rose-700/40',
    },
    cancelled: {
      label: t('transaction.status.cancelled'),
      icon: XCircle,
      className: 'bg-rose-500/15 text-rose-400 border-rose-700/40',
    },
  }
  return map[props.status]
})
</script>

<template>
  <Badge variant="outline" class="inline-flex items-center gap-1.5 rounded-full px-2.5 py-1 text-xs" :class="[meta.className]">
    <component :is="meta.icon" class="h-3.5 w-3.5" />
    {{ meta.label }}
  </Badge>
</template>
