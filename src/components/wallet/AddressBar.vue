<script setup lang="ts">
import { ChevronDown, Copy, LogOut } from 'lucide-vue-next'
import { computed, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { cn } from '@/lib/utils'

const props = defineProps<{ addresses?: string[] }>()
const emit = defineEmits<{ copied: [] }>()

const { t } = useI18n()

const addresses = computed(() => props.addresses ?? ['UQxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx'])
const current = ref(addresses.value[0]!)
const copied = ref(false)

function truncate(addr: string, left = 8, right = 6) {
  if (addr.length <= left + right + 3)
    return addr
  return `${addr.slice(0, left)}...${addr.slice(-right)}`
}

const truncated = computed(() => truncate(current.value))

async function copy() {
  await navigator.clipboard.writeText(current.value)
  copied.value = true
  emit('copied')
  setTimeout(() => (copied.value = false), 1500)
}
</script>

<template>
  <div class="flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between">
    <div class="flex w-full items-center gap-2">
      <DropdownMenu>
        <DropdownMenuTrigger as-child>
          <button
            :class="cn(
              'flex w-full items-center justify-between rounded-md border',
              'border-slate-700 bg-[#0f1216] px-3 py-2 text-left text-sm',
              'focus:outline-none focus:ring-2 focus:ring-emerald-600',
            )"
            :aria-label="t('wallet.selectWalletAddress')"
          >
            <span class="font-medium text-slate-200">
              {{ t('wallet.address') }} <span class="text-slate-300">{{ truncated }}</span>
            </span>
            <ChevronDown class="h-4 w-4 text-slate-400" />
          </button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start" class="w-[360px]">
          <DropdownMenuItem @click="copy()">
            <LogOut class="mr-2 h-4 w-4" />
            {{ t('wallet.disconnectCurrentAddress') }}
          </DropdownMenuItem>
          <DropdownMenuItem @click="copy()">
            <Copy class="mr-2 h-4 w-4" />
            {{ t('wallet.copyCurrentAddress') }}
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  </div>
</template>
