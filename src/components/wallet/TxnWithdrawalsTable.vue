<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { getUserTransactions } from '@/repositories/transaction'
import { formatDate } from '@/utils'
import IconTon from '../shared/Icon/IconTon.vue'
import StatusBadge from './StatusBadge.vue'

const { t } = useI18n()

const transactions = await getUserTransactions({
  type: 'withdrawal',
})
</script>

<template>
  <Table class="rounded-lg border border-slate-700">
    <TableHeader class="bg-[#12161b]">
      <TableRow class="border-slate-700">
        <TableHead class="text-slate-300">
          {{ t('transaction.table.headers.status') }}
        </TableHead>
        <TableHead class="text-slate-300">
          {{ t('transaction.table.headers.date') }}
        </TableHead>
        <TableHead class="text-slate-300">
          {{ t('transaction.table.headers.amount') }}
        </TableHead>
      </TableRow>
    </TableHeader>
    <TableBody>
      <TableRow v-for="t in transactions.data" :key="t.id" class="border-slate-800">
        <TableCell>
          <StatusBadge :status="t.status" />
        </TableCell>
        <TableCell class="align-top text-sm text-slate-300">
          <div class="font-medium">
            {{ formatDate(t.created_at) }}
          </div>
        </TableCell>
        <TableCell class="text-sm">
          <div class="flex items-center gap-2">
            <span class="font-medium">{{ Number(t.value).toFixed(2) }}</span>
            <IconTon class="w-4 h-4" />
          </div>
        </TableCell>
      </TableRow>
      <TableRow v-if="transactions.data.length === 0">
        <TableCell :colspan="5" class="h-28 text-center text-slate-400">
          {{ t('transaction.table.noResults') }}
        </TableCell>
      </TableRow>
    </TableBody>
  </Table>
</template>
