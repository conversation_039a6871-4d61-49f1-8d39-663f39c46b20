<script setup lang="ts">
import { AlignJustifyIcon, CircleUserRoundIcon, EarthIcon, InfoIcon, Volume2Icon } from 'lucide-vue-next'
</script>

<template>
  <header class="bg-page-primary text-white py-4 px-4 flex justify-between items-center fixed top-0 left-0 w-full z-20">
    <!-- Menu <PERSON> -->
    <button class="p-2">
      <AlignJustifyIcon class="h-6 w-6" />
    </button>

    <!-- Header Right Icons -->
    <div class="flex items-center space-x-3">
      <!-- Volume Button -->
      <button class="p-2 bg-gray-800 rounded-full">
        <Volume2Icon class="h-5 w-5" />
      </button>

      <!-- Profile Button -->
      <button class="p-2 bg-gray-800 rounded-full">
        <CircleUserRoundIcon class="h-5 w-5" />
      </button>

      <!-- Info Button -->
      <button class="p-2 bg-gray-800 rounded-full">
        <InfoIcon class="h-5 w-5" />
      </button>

      <!-- Language Selector -->
      <button class="p-2 bg-gray-800 rounded-full flex items-center">
        <EarthIcon class="h-5 w-5 mr-1" />
        <span class="text-sm font-semibold">EN</span>
      </button>
    </div>
  </header>
</template>

<style scoped>
/* Add any component-specific styles here */
</style>
