<script setup lang="ts">
import { Co<PERSON>, Copy, Medal, TrendingDown, TrendingUp, Zap } from 'lucide-vue-next'
import { computed } from 'vue'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { But<PERSON> } from '@/components/ui/button'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { cn } from '@/lib/utils'

interface User {
  id: string
  name: string
  handle: string
  speedThs: number
  ton: number
  trend: 'up' | 'down' | 'flat'
  avatarUrl?: string
}

const props = defineProps<{
  user: User
  rank: number
  mode: 'speed' | 'invites'
}>()

const isTop = computed(() => props.rank <= 3)

function palette(rank: number) {
  return rank === 1
    ? 'bg-amber-500 text-amber-50'
    : rank === 2
      ? 'bg-slate-400 text-slate-900'
      : rank === 3
        ? 'bg-orange-500 text-orange-50'
        : 'bg-muted text-foreground'
}

function getInitials(name: string) {
  const parts = name.trim().split(/\s+/)
  const first = parts[0]?.[0] ?? ''
  const last = parts.length > 1 ? parts[parts.length - 1][0] ?? '' : ''
  return (first + last).toUpperCase()
}

function copyHandle() {
  window.navigator?.clipboard?.writeText(props.user.handle)
}
</script>

<template>
  <div
    :class="cn(
      'relative rounded-xl border bg-muted/40',
      'hover:bg-muted/60 transition-colors',
      'flex items-center justify-between p-3 md:p-4',
      'before:absolute before:left-0 before:top-0 before:h-full before:w-1.5 before:rounded-l-xl',
      isTop
        ? (rank === 1
          ? 'before:bg-gradient-to-b before:from-yellow-400 before:to-amber-600'
          : (rank === 2
            ? 'before:bg-gradient-to-b before:from-slate-200 before:to-slate-400'
            : 'before:bg-gradient-to-b before:from-orange-300 before:to-amber-500'))
        : 'before:bg-border',
    )"
    :aria-label="`Rank ${rank}, ${user.name}`"
  >
    <div class="flex items-center gap-3 md:gap-4 min-w-0">
      <div class="grid h-8 w-8 place-items-center rounded-full text-xs font-bold shadow-sm" :class="[palette(rank)]" aria-hidden="true">
        {{ rank }}
      </div>
      <Avatar class="h-10 w-10">
        <AvatarImage :src="user.avatarUrl || '/images/stars.svg'" :alt="`${user.name} avatar`" />
        <AvatarFallback>{{ getInitials(user.name) }}</AvatarFallback>
      </Avatar>
      <div class="min-w-0">
        <div class="flex items-center gap-2">
          <p class="truncate font-medium">
            {{ user.name }}
          </p>
          <Badge
            v-if="rank <= 3"
            variant="secondary"
            :class="cn(
              'gap-1',
              rank === 1 ? 'bg-yellow-500/15 text-yellow-500' : '',
              rank === 2 ? 'bg-slate-300/15 text-slate-300' : '',
              rank === 3 ? 'bg-amber-500/15 text-amber-500' : '',
            )"
          >
            <Medal class="h-3.5 w-3.5" aria-hidden="true" /> Top {{ rank }}
          </Badge>
        </div>
        <p class="text-xs text-muted-foreground truncate">
          {{ user.handle }}
        </p>
      </div>
    </div>

    <div class="flex items-center gap-3 shrink-0">
      <div class="flex items-center gap-1.5 text-sm font-medium">
        <Zap class="h-4 w-4 text-amber-500" aria-hidden="true" />
        {{ user.speedThs.toFixed(2) }} <span class="text-muted-foreground">TH/s</span>
        <span
          v-if="user.trend !== 'flat'"
          :class="cn(
            'ml-1 inline-flex items-center gap-0.5 rounded-md px-1.5 py-0.5 text-xs',
            user.trend === 'up' ? 'bg-emerald-500/10 text-emerald-500' : 'bg-rose-500/10 text-rose-500',
          )"
        >
          <TrendingUp v-if="user.trend === 'up'" class="h-3 w-3" aria-hidden="true" />
          <TrendingDown v-else class="h-3 w-3" aria-hidden="true" />
          1.2%
        </span>
      </div>
      <div class="flex items-center gap-1.5 text-sm font-medium">
        <Coins class="h-4 w-4 text-emerald-500" aria-hidden="true" />
        <span class="tabular-nums">{{ user.ton }}</span>
        <span class="text-muted-foreground">{{ mode === 'invites' ? 'Invites' : 'Ton' }}</span>
      </div>

      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger as-child>
            <Button size="icon" variant="ghost" class="h-8 w-8" @click="copyHandle">
              <Copy class="h-4 w-4" aria-hidden="true" />
              <span class="sr-only">Copy handle</span>
            </Button>
          </TooltipTrigger>
          <TooltipContent side="left">
            Copy handle
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </div>
  </div>
</template>
