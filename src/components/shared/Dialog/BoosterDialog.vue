<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { computed, reactive, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import InputField from '@/components/shared/Form/InputField.vue'
import { Button } from '@/components/ui/button'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { useGlobalDialog } from '@/composables/useGlobalDialog'
import { useTonConnect } from '@/composables/useTonConnect'
import { completeBoostPayment, initBoostPayment } from '@/repositories/tonPayment'
import { useBootstrapStore } from '@/stores/useBootstrapStore'

interface Props {
  open?: boolean
  currentSpeed?: number | string
  balance?: number
}

interface Emits {
  (e: 'update:open', value: boolean): void
  (e: 'success', data: { newSpeed: number, expiresAt: string }): void
}

const props = withDefaults(defineProps<Props>(), {
  open: false,
  currentSpeed: 0,
  balance: 0,
})

const emit = defineEmits<Emits>()

const isOpen = computed({
  get: () => props.open,
  set: value => emit('update:open', value),
})

const { t } = useI18n()
const { showSuccess } = useGlobalDialog()
const { clientSettings } = storeToRefs(useBootstrapStore())
const { sendTransaction, connectWallet } = useTonConnect()

// Minimum boost amount from client settings
const minimumAmount = computed(() => {
  const amount = clientSettings.value?.minimum_boot_amount
  return amount || 0
})

const form = reactive({
  amount: minimumAmount.value,
})

const errors = reactive({
  amount: '',
})

const isLoading = ref(false)
const boostError = ref('')

// Payment states
const preparingPayment = ref(false)
const sendingTransaction = ref(false)
const completingPayment = ref(false)
const currentTransaction = ref<{ hash: string, address: string, message: string, ton_amount: number } | null>(null)

// Calculate expected mining power boost (example calculation)
const expectedSpeed = computed(() => {
  const convertRate = clientSettings.value?.convert_rate
  if (!convertRate) {
    return 0
  }
  return (form.amount * convertRate * 1000_000 / (86400 * 30))
})

const expectedSpeedFormatted = computed(() => {
  const speed = expectedSpeed.value

  if (speed >= 1000) {
    // Convert to PH/s (1 PH = 1000 TH)
    const phValue = speed / 1000
    return `${phValue.toFixed(1)} PH/s`
  }
  else if (speed >= 1) {
    // Display as TH/s for values 1-999
    return `${speed.toFixed(1)} TH/s`
  }
  else {
    // Convert to GH/s (1 TH = 100 GH, so 1 GH = 0.01 TH)
    const ghValue = speed * 100
    return `${ghValue.toFixed(1)} GH/s`
  }
})

const expectedMonthlyProfit = computed(() => {
  const convertRate = clientSettings.value?.convert_rate
  if (!convertRate) {
    return '0.0000'
  }
  return (form.amount * convertRate).toFixed(4)
})

const expectedDailyProfit = computed(() => {
  return (Number.parseFloat(expectedMonthlyProfit.value) / 30).toFixed(5)
})

const isPaymentInProgress = computed(() => {
  return preparingPayment.value || sendingTransaction.value || completingPayment.value
})

async function handleBoost() {
  // Clear previous errors
  clearErrors()

  if (!validateForm()) {
    return
  }

  isLoading.value = true
  boostError.value = ''

  connectWallet(async () => {
    let success = false
    try {
    // Step 1: Initialize payment with backend
      const transaction = await initBoostPayment(form.amount)

      // Step 2: Send TON transaction
      success = Boolean(await sendTransaction(
        transaction.address,
        transaction.message,
        form.amount * 1e9,
      ))
    }
    catch (error) {
      console.error('Boost payment failed:', error)
      boostError.value = error instanceof Error ? error.message : t('boost.payment_failed')
    }
    finally {
      isLoading.value = false
      preparingPayment.value = false
      sendingTransaction.value = false
    }

    // Step 3: Complete payment with backend
    if (success) {
      await completePayment()

      // Success - emit success event and close dialog
      emit('success', { newSpeed: Number(props.currentSpeed) + expectedSpeed.value, expiresAt: new Date().toISOString() })
      showSuccess(t('boost.payment_success'))
      closeDialog()
    }
    completingPayment.value = false
  })
}

async function completePayment() {
  if (!currentTransaction.value) {
    throw new Error(t('boost.no_transaction'))
  }

  completingPayment.value = true

  try {
    const response = await completeBoostPayment(currentTransaction.value.hash)

    if (response.status !== 'completed') {
      throw new Error(t('boost.payment_not_completed'))
    }

    // User balance will be updated by the parent component via the success event
  }
  catch (error) {
    throw new Error(error instanceof Error ? error.message : t('boost.complete_payment_failed'))
  }
  finally {
    completingPayment.value = false
  }
}

function clearErrors() {
  errors.amount = ''
  boostError.value = ''
  currentTransaction.value = null
}

function validateForm() {
  clearErrors()

  // Ensure amount is a valid number
  const amount = Number(form.amount)
  if (!form.amount || Number.isNaN(amount) || amount <= 0) {
    errors.amount = t('boost.amount.required')
    return false
  }

  // Update form.amount to the parsed number to avoid NaN issues
  form.amount = amount

  if (amount < minimumAmount.value) {
    errors.amount = t('boost.amount.minimum', { minimumAmount: minimumAmount.value })
    return false
  }

  if (amount > props.balance) {
    errors.amount = t('boost.amount.insufficient')
    return false
  }

  return true
}

function closeDialog() {
  isOpen.value = false
  form.amount = minimumAmount.value || 0.1
  clearErrors()
}
</script>

<template>
  <Dialog v-model:open="isOpen">
    <DialogContent class="sm:max-w-md text-white border-white/20">
      <DialogHeader>
        <DialogTitle class="text-center text-lg font-medium text-white">
          {{ t('boost.title') }}
        </DialogTitle>
        <DialogDescription class="text-center text-gray-400 text-sm">
          {{ t('boost.description') }}
        </DialogDescription>
      </DialogHeader>

      <div class="space-y-4 py-4">
        <!-- General Error Message -->
        <div v-if="boostError" class="p-3 bg-red-900/50 border border-red-700 rounded-md text-red-200 text-sm">
          {{ boostError }}
        </div>

        <!-- Mining Speed Info -->
        <div class="p-4 bg-gray-800/50 rounded-lg space-y-2">
          <div class="flex items-center justify-center">
            <span class="text-2xl font-bold text-blue-400">{{ expectedSpeedFormatted }}</span>
            <svg class="w-6 h-6 ml-2 text-yellow-400" fill="currentColor" viewBox="0 0 24 24">
              <path d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
          </div>
          <div class="text-center text-sm text-gray-400">
            <p>{{ t('boost.monthly_profit') }}: {{ expectedMonthlyProfit }} Ton</p>
            <p>{{ t('boost.daily_profit') }}: {{ expectedDailyProfit }} Ton</p>
          </div>
        </div>

        <!-- Amount Input -->
        <InputField
          v-model="form.amount"
          label="Amount"
          type="number"
          :placeholder="minimumAmount.toString()"
          :error="errors.amount"
          :helper-text="t('boost.amount.minimum_text', { minimumAmount })"
          :disabled="isLoading || isPaymentInProgress"
        />
      </div>

      <DialogFooter class="space-y-2">
        <Button
          variant="ghost"
          class="w-full text-gray-400 hover:text-white hover:bg-gray-700"
          :disabled="isLoading"
          @click="closeDialog"
        >
          {{ t('common.back') }}
        </Button>
        <Button
          :disabled="isLoading || isPaymentInProgress"
          @click="handleBoost"
        >
          <span v-if="preparingPayment">{{ t('boost.preparing_payment') }}</span>
          <span v-else-if="sendingTransaction">{{ t('boost.sending_transaction') }}</span>
          <span v-else-if="completingPayment">{{ t('boost.completing_payment') }}</span>
          <span v-else-if="isLoading">{{ t('boost.processing') }}</span>
          <span v-else>{{ t('boost.purchase_boost') }}</span>
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>
