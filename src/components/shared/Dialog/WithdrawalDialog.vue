<script setup lang="ts">
import type { Token } from '@/types'
import { storeToRefs } from 'pinia'
import { computed, reactive, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import InputField from '@/components/shared/Form/InputField.vue'
import IconShiba from '@/components/shared/Icon/IconShiba.vue'
import IconTon from '@/components/shared/Icon/IconTon.vue'
import { Button } from '@/components/ui/button'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { useGlobalDialog } from '@/composables/useGlobalDialog'
import { createWithdrawal } from '@/repositories/withdrawal'
import { useBootstrapStore } from '@/stores/useBootstrapStore'

interface Props {
  open?: boolean
  balance?: number
  token?: Token
}

interface Emits {
  (e: 'update:open', value: boolean): void
}

const props = withDefaults(defineProps<Props>(), {
  open: false,
  balance: 0,
  token: 'shiba',
})

const emit = defineEmits<Emits>()

const isOpen = computed({
  get: () => props.open,
  set: value => emit('update:open', value),
})

const { t } = useI18n()
const { showSuccess, showError } = useGlobalDialog()

const { clientSettings } = storeToRefs(useBootstrapStore())

const walletInfoMap = {
  shiba: {
    name: 'SHIBA',
    title: t('withdrawal.shiba.title'),
    descriptions: [
      t('withdrawal.shiba.description'),
    ],
    minimumAmount: clientSettings.value?.minimum_withdrawal_shiba_amount || 0,
  },
  ton: {
    name: 'TON',
    title: t('withdrawal.ton.title'),
    descriptions: [
      t('withdrawal.ton.description'),
      t('withdrawal.ton.description2'),
    ],
    minimumAmount: clientSettings.value?.minimum_withdrawal_ton_amount || 0,
  },
}

const form = reactive({
  walletAddress: '',
  amount: 0,
})

const errors = reactive({
  walletAddress: '',
  amount: '',
})

const isLoading = ref(false)
const withdrawalError = ref('')

const minimumAmount = computed(() => walletInfoMap[props.token].minimumAmount)

async function handleWithdraw() {
  // Clear previous errors
  clearErrors()

  if (!validateForm()) {
    return
  }

  isLoading.value = true
  withdrawalError.value = ''

  try {
    await createWithdrawal({
      amount: form.amount,
      wallet_address: form.walletAddress,
      notes: `${walletInfoMap[props.token].name} withdrawal request`,
    })

    // Success: close dialog and reset form
    closeDialog()

    // Show success notification
    showSuccess(
      t('withdrawal.success.title'),
      t('withdrawal.success.message', {
        amount: form.amount.toLocaleString(),
        token: walletInfoMap[props.token].name,
      }),
    )

    // TODO: Refresh user balance
  }
  catch (error: any) {
    const errorMessage = error.message || 'Failed to create withdrawal request'
    withdrawalError.value = errorMessage

    // Show error notification
    showError(
      t('withdrawal.error.title'),
      errorMessage,
    )
  }
  finally {
    isLoading.value = false
  }
}

function clearErrors() {
  errors.walletAddress = ''
  errors.amount = ''
  withdrawalError.value = ''
}

function validateForm() {
  clearErrors()

  if (!form.walletAddress) {
    errors.walletAddress = t('withdrawal.walletAddress.required')
  }

  if (!form.amount) {
    errors.amount = t('withdrawal.amount.required')
  }

  if (form.amount < minimumAmount.value) {
    errors.amount = t('withdrawal.amount.minimum', { minimumAmount: minimumAmount.value, token: walletInfoMap[props.token].name })
  }

  if (form.amount > props.balance) {
    errors.amount = t('withdrawal.amount.insufficient')
  }

  return Object.values(errors).every(error => error === '')
}

function closeDialog() {
  isOpen.value = false
  form.walletAddress = ''
  form.amount = 0

  clearErrors()
}
</script>

<template>
  <Dialog v-model:open="isOpen">
    <DialogContent class="sm:max-w-md text-white border-white/20">
      <DialogHeader>
        <div class="flex items-center justify-center">
          <IconShiba v-if="token === 'shiba'" class="w-10 h-10" />
          <IconTon v-else class="w-10 h-10" />
        </div>
        <DialogTitle class="text-center text-lg font-medium text-white">
          {{ walletInfoMap[token].title }}
        </DialogTitle>
        <DialogDescription class="text-center text-gray-400 text-sm">
          <p v-for="description in walletInfoMap[token].descriptions" :key="description">
            {{ description }}
          </p>
        </DialogDescription>
      </DialogHeader>

      <div class="space-y-4 py-4">
        <!-- General Error Message -->
        <div v-if="withdrawalError" class="p-3 bg-red-900/50 border border-red-700 rounded-md text-red-200 text-sm">
          {{ withdrawalError }}
        </div>

        <!-- Wallet Address Input -->
        <InputField
          v-model="form.walletAddress"
          label="Wallet"
          placeholder="Enter wallet address"
          :error="errors.walletAddress"
          :disabled="isLoading"
        />

        <!-- Amount Input -->
        <InputField
          v-model="form.amount"
          label="Amount"
          type="number"
          placeholder="Enter amount"
          :error="errors.amount"
          :helper-text="t('withdrawal.amount.minimum', { minimumAmount: minimumAmount.toLocaleString(), token: walletInfoMap[token].name })"
          :disabled="isLoading"
        />
      </div>

      <DialogFooter class="space-y-2">
        <Button
          variant="ghost"
          class="w-full text-gray-400 hover:text-white hover:bg-gray-700"
          :disabled="isLoading"
          @click="closeDialog"
        >
          Back
        </Button>
        <Button
          class="w-full"
          :disabled="isLoading"
          @click="handleWithdraw"
        >
          {{ isLoading ? 'Processing...' : 'Request Withdraw' }}
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>
