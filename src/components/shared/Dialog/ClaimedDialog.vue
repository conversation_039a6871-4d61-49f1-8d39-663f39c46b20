<script setup lang="ts">
import type { MiningClaimsResponse } from '@/types/user'
import { storeToRefs } from 'pinia'
import { computed, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { Button } from '@/components/ui/button'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { useGlobalDialog } from '@/composables/useGlobalDialog'
import { claimMiningTokens } from '@/repositories/user'
import { useBootstrapStore } from '@/stores/useBootstrapStore'
import { useUserStore } from '@/stores/userStore'

interface Props {
  open?: boolean
  pendingTokens?: number
}

interface Emits {
  (e: 'update:open', value: boolean): void
  (e: 'success', data: MiningClaimsResponse): void
}

const props = withDefaults(defineProps<Props>(), {
  open: false,
  pendingTokens: 0,
})

const emit = defineEmits<Emits>()

const userStore = useUserStore()
const { clientSettings } = storeToRefs(useBootstrapStore())

const minimumAmount = computed(() => {
  return clientSettings.value?.minimum_claim_ton_amount || 0.1
})

const isOpen = computed({
  get: () => props.open,
  set: value => emit('update:open', value),
})

const { t } = useI18n()
const { showSuccess } = useGlobalDialog()

const isLoading = ref(false)
const claimError = ref('')

// Check if user has enough tokens to claim
const canClaim = computed(() => {
  return props.pendingTokens >= minimumAmount.value
})

async function handleClaim() {
  if (!canClaim.value) {
    claimError.value = t('claim.insufficient_tokens', { minimum: minimumAmount.value })
    return
  }

  isLoading.value = true
  claimError.value = ''

  try {
    const response = await claimMiningTokens()

    // Success - emit success event and close dialog
    // Update user data with new balance and reset pending tokens
    if (userStore.user) {
      userStore.user.balance_ton = response.new_balance.toString()
      userStore.user.pending_token = response.pending_token.toString()
      userStore.user.last_checkpoint = response.claimed_at
    }
    showSuccess(t('claim.success', { amount: response.claimed_amount }))
    closeDialog()
  }
  catch (error) {
    console.error('Claim mining tokens failed:', error)
    claimError.value = error instanceof Error ? error.message : t('claim.failed')
  }
  finally {
    isLoading.value = false
  }
}

function closeDialog() {
  isOpen.value = false
  claimError.value = ''
}
</script>

<template>
  <Dialog v-model:open="isOpen">
    <DialogContent class="sm:max-w-md text-white border-white/20">
      <DialogHeader>
        <DialogTitle class="text-center text-lg text-white font-semibold">
          {{ t('claim.title') }}
        </DialogTitle>
        <DialogDescription class="text-center text-muted text-sm">
          {{ t('claim.description') }}
        </DialogDescription>
      </DialogHeader>

      <div class="space-y-4">
        <!-- General Error Message -->
        <div v-if="claimError" class="p-3 bg-red-900/50 border border-red-700 rounded-md text-red-200 text-sm">
          {{ claimError }}
        </div>

        <!-- Minimum Amount Info -->
        <div class="text-center text-sm font-semibold">
          <p>{{ t('claim.minimum_amount', { amount: minimumAmount }) }}</p>
        </div>

        <!-- Insufficient Tokens Warning -->
        <div v-if="!canClaim" class="p-3 bg-yellow-900/50 border border-yellow-700 rounded-md text-yellow-200 text-sm">
          {{ t('claim.insufficient_warning', { minimum: minimumAmount }) }}
        </div>
      </div>

      <DialogFooter class="space-y-2">
        <Button
          variant="ghost"
          class="w-full text-gray-400 hover:text-white hover:bg-gray-700"
          :disabled="isLoading"
          @click="closeDialog"
        >
          {{ t('common.back') }}
        </Button>
        <Button
          :disabled="isLoading || !canClaim"
          class="w-full"
          @click="handleClaim"
        >
          <span v-if="isLoading">{{ t('claim.processing') }}</span>
          <span v-else>{{ t('claim.claim_button') }}</span>
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>
