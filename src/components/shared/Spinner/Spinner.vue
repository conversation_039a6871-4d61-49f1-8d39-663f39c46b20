<script setup lang="ts">
import type { HTMLAttributes } from 'vue'
import { computed } from 'vue'
import { cn } from '@/lib/utils'
import IconTon from '../Icon/IconTon.vue'

interface Props {
  duration?: number // Duration in seconds
  class?: HTMLAttributes['class']
}

const props = withDefaults(defineProps<Props>(), {
  duration: 2, // Default 2 seconds
})

const spinStyle = computed(() => ({
  animationDuration: `${props.duration}s`,
}))
</script>

<template>
  <div
    :class="cn('inline-flex items-center justify-center relative overflow-hidden', props.class)"
  >
    <img
      src="/images/spinner.png"
      alt="Loading..."
      class="animate-spin w-full h-full"
      :style="spinStyle"
    >
    <IconTon class="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 w-1/4" />
  </div>
</template>
