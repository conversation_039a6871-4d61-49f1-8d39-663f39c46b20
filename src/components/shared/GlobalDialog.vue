<script setup lang="ts">
import { CheckCircle2, XCircle } from 'lucide-vue-next'
import Button from '@/components/ui/button/Button.vue'
import Dialog from '@/components/ui/dialog/Dialog.vue'
import DialogClose from '@/components/ui/dialog/DialogClose.vue'
import DialogContent from '@/components/ui/dialog/DialogContent.vue'
import DialogDescription from '@/components/ui/dialog/DialogDescription.vue'
import DialogFooter from '@/components/ui/dialog/DialogFooter.vue'
import DialogHeader from '@/components/ui/dialog/DialogHeader.vue'
import DialogTitle from '@/components/ui/dialog/DialogTitle.vue'
import { useGlobalDialog } from '@/composables/useGlobalDialog'

const { dialogState, closeDialog } = useGlobalDialog()
</script>

<template>
  <Dialog v-model:open="dialogState.isOpen">
    <DialogContent class="sm:max-w-md border-white/20">
      <DialogHeader>
        <DialogTitle class="flex items-center gap-2">
          <CheckCircle2
            v-if="dialogState.type === 'success'"
            class="size-5 text-green-600"
          />
          <XCircle
            v-else
            class="size-5 text-red-600"
          />
          {{ dialogState.title }}
        </DialogTitle>
        <DialogDescription v-if="dialogState.message">
          {{ dialogState.message }}
        </DialogDescription>
      </DialogHeader>
      <DialogFooter>
        <DialogClose as-child>
          <Button
            :variant="dialogState.type === 'success' ? 'default' : 'destructive'"
            @click="closeDialog"
          >
            OK
          </Button>
        </DialogClose>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>
