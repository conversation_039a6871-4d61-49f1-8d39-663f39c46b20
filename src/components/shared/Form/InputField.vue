<script setup lang="ts">
import { computed } from 'vue'
import { Input } from '@/components/ui/input'

interface Props {
  label?: string
  modelValue?: string | number
  type?: string
  placeholder?: string
  error?: string
  helperText?: string
  required?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: string | number | undefined): void
}

const props = withDefaults(defineProps<Props>(), {
  type: 'text',
  required: false,
})

const emit = defineEmits<Emits>()

const value = computed({
  get: () => props.modelValue,
  set: value => emit('update:modelValue', value),
})
</script>

<template>
  <div class="space-y-2">
    <label v-if="label" class="text-sm text-white font-medium">
      {{ label }}
      <span v-if="required" class="text-destructive">*</span>
    </label>
    <Input
      v-model="value"
      :type="type"
      :placeholder="placeholder"
      :class="{ 'border-destructive': error }"
    />
    <p v-if="error" class="text-xs text-destructive font-medium">
      {{ error }}
    </p>
    <p v-else-if="helperText" class="text-xs text-white/70">
      {{ helperText }}
    </p>
  </div>
</template>
