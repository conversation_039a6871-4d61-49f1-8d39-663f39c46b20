<script setup lang="ts">
import Spinner from './Spinner/Spinner.vue'

defineProps<{
  isLoading: boolean
}>()
</script>

<template>
  <div class="h-screen flex items-center justify-center">
    <div class="w-1/2 mx-auto relative">
      <Spinner :duration="7" />
      <div class="text-white absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 text-nowrap mt-24 space-y-16">
        <div v-if="!isLoading" class="text-2xl">
          Not Allowed Platform !
        </div>
        <div class="text-center">
          <div class="font-semibold">
            Please Wait...!
          </div>
          <div class="font-light text-sm">
            version 2.5
          </div>
        </div>
      </div>
    </div>
    <!-- overlay -->
    <div class="absolute inset-0 bg-green-800/20" />
  </div>
</template>
