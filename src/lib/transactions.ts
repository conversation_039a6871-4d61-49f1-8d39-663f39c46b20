export type TxnType = 'deposit' | 'withdraw' | 'bonus'
export type TxnStatus = 'completed' | 'pending' | 'failed' | 'cancelled'

export interface Txn {
  id: string
  type: TxnType
  createdAt: number // epoch ms
  amount: number
  unit: string // token/unit symbol
  boost: number
  boostUnit: string
  status: TxnStatus
  address: string
  note?: string
}

export function formatDate(ts: number) {
  const d = new Date(ts)
  const dd = String(d.getDate()).padStart(2, '0')
  const mm = String(d.getMonth() + 1).padStart(2, '0')
  const yy = String(d.getFullYear()).slice(2)
  const hh = String(d.getHours()).padStart(2, '0')
  const min = String(d.getMinutes()).padStart(2, '0')
  return `${yy}-${mm}-${dd} ${hh}:${min}`
}

export function formatBoost(boost: number, unit = 'TH/s') {
  return `${boost.toLocaleString(undefined, { maximumFractionDigits: 2 })} ${unit}`
}

export function sampleTransactions(): Txn[] {
  const now = Date.now()
  const baseAddr = 'UQBFSgckrQ2hX3pBq9nJ9p8QG9k4rPPCJFHTE'
  return [
    {
      id: 'tx_0002',
      type: 'deposit',
      createdAt: now - 60_000 * 1, // 1 min ago
      amount: 3,
      unit: '◇',
      boost: 2.4,
      boostUnit: 'TH/s',
      status: 'pending',
      address: baseAddr,
      note: 'Network confirmation...',
    },
    {
      id: 'tx_0001',
      type: 'deposit',
      createdAt: now - 60_000 * 3, // 3 min ago
      amount: 3,
      unit: '◇',
      boost: 2.4,
      boostUnit: 'TH/s',
      status: 'pending',
      address: baseAddr,
      note: 'Network confirmation...',
    },
    // Additional examples to showcase functionality
    {
      id: 'tx_0003',
      type: 'withdraw',
      createdAt: now - 60_000 * 60 * 8,
      amount: 1.25,
      unit: '◇',
      boost: 0,
      boostUnit: 'TH/s',
      status: 'completed',
      address: 'UQC6AvVxM5zYt4i1oNsWxY2z3pZ7hZPp7ZPE',
      note: 'Payout to exchange',
    },
    {
      id: 'tx_0004',
      type: 'bonus',
      createdAt: now - 60_000 * 60 * 24,
      amount: 0.35,
      unit: '◇',
      boost: 0.4,
      boostUnit: 'TH/s',
      status: 'completed',
      address: baseAddr,
      note: 'Referral reward',
    },
    {
      id: 'tx_0005',
      type: 'withdraw',
      createdAt: now - 60_000 * 15,
      amount: 0.8,
      unit: '◇',
      boost: 0,
      boostUnit: 'TH/s',
      status: 'failed',
      address: baseAddr,
      note: 'Insufficient balance',
    },
  ]
}

export function computeStats(txns: Txn[]) {
  const totalDeposited = txns.filter(t => t.type === 'deposit').reduce((acc, t) => acc + t.amount, 0)
  const currentBoost = txns.reduce((acc, t) => acc + t.boost, 0)
  const pendingCount = txns.filter(t => t.status === 'pending').length
  return { totalDeposited, currentBoost, pendingCount }
}
