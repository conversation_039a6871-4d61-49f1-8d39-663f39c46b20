<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { onMounted } from 'vue'
import { RouterView } from 'vue-router'
import AppLayout from './components/layout/AppLayout.vue'
import LoadingScreen from './components/shared/LoadingScreen.vue'
import { useTelegramInitialization } from './composables/useAppInitialization'
import { useBootstrapStore } from './stores/useBootstrapStore'

const { isTelegramLoading, initializeTelegram, isTelegramInitialized } = useTelegramInitialization()
const { isInitialized } = storeToRefs(useBootstrapStore())

onMounted(async () => {
  await initializeTelegram()
})
</script>

<template>
  <div v-if="!isTelegramInitialized || !isInitialized">
    <LoadingScreen :is-loading="isTelegramLoading" />
  </div>
  <AppLayout v-else>
    <RouterView />
  </AppLayout>
</template>
