/// <reference types="vite/client" />

// Minimal typings for Telegram Mini App global to satisfy TS
declare global {
  interface Window {
    Telegram?: {
      WebApp?: {
        platform: string
        version: string
        colorScheme: string
        initDataUnsafe?: { user?: any }
        ready: () => Promise<void> | void
        expand: () => Promise<void> | void
        openInvoice: (url: string, callback?: (status: string) => void) => void
      }
    }
  }
}
export {}
