// Example usage of the enhanced Bearer token authentication system
// This file demonstrates how to use the updated API client and user store

// Option 1: Using the createApiClient utility (recommended)
import { createApiClient } from '@/utils/apiClient'

// Option 2: Using the composable API client (for Vue components)
import { useApi<PERSON>lient } from '@/composables/useApiClient'

import { useUserStore } from '@/stores/userStore'
import type { SyncUserRequest, SyncUserResponse } from '@/types/user'
import type { ApiResponse } from '@/types/api'

// Example: User authentication/sync using createApiClient utility
export async function authenticateUser(userData: SyncUserRequest) {
  try {
    // Create API client instance
    const apiClient = createApiClient()

    // Make the API call
    const response = await apiClient.post<ApiResponse<SyncUserResponse>>('/user/sync', userData)

    // Authentication data is automatically handled by the API client
    console.warn('User authenticated:', response.data.action)
    return response
  } catch (error) {
    console.error('Authentication failed:', error)
    throw error
  }
}

// Example: User authentication/sync using composable API client (recommended)
export async function authenticateUserWithComposable(userData: SyncUserRequest) {
  const api = useApiClient()

  try {
    // Make the API call
    const response = await api.post<SyncUserResponse>('/user/sync', userData)

    // The handleAuthResponse method will automatically extract and store the token
    const processedResponse = api.handleAuthResponse(response)

    console.warn('User authenticated:', processedResponse.data.action)
    return processedResponse
  } catch (error) {
    console.error('Authentication failed:', error)
    throw error
  }
}

// Example: Making authenticated API calls with createApiClient
export async function makeAuthenticatedRequest() {
  const userStore = useUserStore()

  // Check if user is authenticated before making the request
  if (!userStore.isAuthenticated) {
    throw new Error('User is not authenticated')
  }

  try {
    // Create API client instance - Authorization header will be automatically added
    const apiClient = createApiClient()
    const response = await apiClient.get('/user/profile')
    return response
  } catch (error) {
    // If the token is expired, the response interceptor will automatically clear it
    console.error('Authenticated request failed:', error)
    throw error
  }
}

// Example: Making authenticated API calls with composable (recommended)
export async function makeAuthenticatedRequestWithComposable() {
  const userStore = useUserStore()
  const api = useApiClient()

  // Check if user is authenticated before making the request
  if (!userStore.isAuthenticated) {
    throw new Error('User is not authenticated')
  }

  try {
    // The Authorization header will be automatically added by the request interceptor
    const response = await api.get('/user/profile')
    return response
  } catch (error) {
    // If the token is expired, the response interceptor will automatically clear it
    console.error('Authenticated request failed:', error)
    throw error
  }
}

// Example: Check token status
export function checkTokenStatus() {
  const userStore = useUserStore()

  if (!userStore.accessToken) {
    console.warn('No authentication token found')
    return { status: 'no_token' }
  }

  if (userStore.isTokenExpired) {
    console.warn('Authentication token has expired')
    return { status: 'expired' }
  }

  const minutesUntilExpiry = Math.floor(userStore.getTimeUntilExpiry() / (1000 * 60))
  console.warn(`Token expires in ${minutesUntilExpiry} minutes`)

  return {
    status: 'valid',
    expiresInMinutes: minutesUntilExpiry
  }
}

// Example: Initialize the app (in-memory authentication only)
export function initializeApp() {
  const userStore = useUserStore()

  // Initialize the store (no localStorage in new pattern)
  userStore.initialize()

  if (userStore.isAuthenticated) {
    console.warn('User is authenticated')
    return { authenticated: true, user: userStore.user }
  } else {
    console.warn('No authentication found - user needs to authenticate via Telegram')
    return { authenticated: false, user: null }
  }
}

// Example: Logout user
export function logoutUser() {
  const userStore = useUserStore()
  userStore.clearAuthData()
  console.warn('User logged out')
}

// Example: Setup token expiry warning
export function setupTokenExpiryWarning() {
  const userStore = useUserStore()

  if (!userStore.isAuthenticated) {
    return
  }

  const timeUntilExpiry = userStore.getTimeUntilExpiry()
  const warningTime = 5 * 60 * 1000 // 5 minutes before expiry

  if (timeUntilExpiry > warningTime) {
    setTimeout(() => {
      console.warn('Authentication token will expire in 5 minutes')
      // You could show a notification to the user here
    }, timeUntilExpiry - warningTime)
  }
}
