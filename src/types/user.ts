// User Sync API types
export interface SyncUserRequest {
  telegram_id: string
  name: string
  country?: string
  language_code?: string
  username?: string
  avatar_url?: string
  referral_code?: string
  device_name?: string
}

export interface UserData {
  id: number
  tele_id: string
  username: string
  name: string
  country: string | null
  language_code: string | null
  avatar_url: string | null
  last_active: string
  balance_ton: string
  balance_token: string
  speed: number
  pending_token: string
  last_checkpoint: string
  created_at: string
  updated_at: string
  referral_code: string
}

export interface SyncUserResponse {
  user: UserData
  token: string
  token_expires_at: string
  action: 'initialized' | 'restored'
  referral?: {
    referred_by: {
      id: number
      name: string
      username: string
    }
    referral_created: boolean
  }
}

export interface SyncUserErrorResponse {
  success: false
  message: string
  errors: Record<string, string[]>
}

// Telegram User Info API types
export interface TelegramUserInfo {
  name: string | null
  username: string
  avatar_url: string | null
}

export interface SyncUserResult {
  user: UserData
  action: 'initialized' | 'restored'
  token: string
  token_expires_at: string
}

// Mining Claims API types
export interface MiningClaimsResponse {
  claimed_amount: number
  new_balance: number
  pending_token: number
  claimed_at: string
}

export interface MiningClaimsErrorResponse {
  success: false
  message: string
  data?: {
    pending_token: number
    current_balance: number
  }
}
