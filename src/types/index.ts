// Telegram Mini App User interface based on the API documentation
export interface UserTelegram {
  id: number
  first_name: string
  last_name?: string
  username?: string
  language_code?: string
  is_premium?: boolean
  is_bot?: boolean
  added_to_attachment_menu?: boolean
  allows_write_to_pm?: boolean
  photo_url?: string
}

export type Token = 'ton' | 'shiba'

// Re-export withdrawal types
export * from './withdrawal'

// Re-export settings types
export * from './settings'
