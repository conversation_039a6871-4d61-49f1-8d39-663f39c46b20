// Settings API types based on /api/public/client-settings endpoint

export interface ClientSettings {
  convert_rate: number
  min_withdrawal_amount: string
  withdrawal_fee: string
  minimum_claim_ton_amount: number
  minimum_withdrawal_ton_amount: number
  minimum_withdrawal_shiba_amount: number
  ton_withdrawal_fee_percent: number
  minimum_boot_amount: number
  ton_wallet_address: string
}

export interface ClientSettingsResponse {
  success: true
  data: ClientSettings
}
