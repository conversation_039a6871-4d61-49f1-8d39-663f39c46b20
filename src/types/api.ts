// General API response types
export interface ApiResponse<T> {
  data: T
  message: string
  success: boolean
}

export interface ApiError {
  success: false
  message: string
  error: string
}

// Laravel-style pagination response (used by withdrawal API)
export interface LaravelPaginatedResponse<T> {
  current_page: number
  data: T[]
  first_page_url: string
  from: number
  last_page: number
  last_page_url: string
  next_page_url?: string | null
  path: string
  per_page: number
  prev_page_url?: string | null
  to: number
  total: number
}

// Boost API types
export interface BoostRequest {
  ton_amount: number
}

export interface BoostResponse {
  transaction_hash: string
  ton_amount: number
  boost_expired_at: string
  new_speed: number
}

export interface BoostValidationError {
  expected_message: string
  expected_amount: number
  ton_amount: number
}
// {
//   "id": 49,
//   "user_id": 4,
//   "staff_id": null,
//   "type": "boost_payment",
//   "value": "2.50000000",
//   "status": "completed",
//   "hash": "tx_HJSGFHKNtxoAaRKoO3tlfZlmZjeDCr1L",
//   "description": "Purchase of Speed Boost for 2.50 TON",
//   "created_at": "2025-08-17T12:17:29.000000Z",
//   "updated_at": "2025-08-17T12:17:29.000000Z",
//   "group": "deposit"
// }
// Transaction API types
export interface Transaction {
  id: number
  user_id: number
  staff_id: number | null
  group: 'deposit' | 'boost' | 'withdrawal' | 'mining_claim'
  value: string
  currency: string
  status: 'pending' | 'completed' | 'failed' | 'cancelled'
  description: string
  created_at: string
  updated_at: string
}

export interface TransactionPagination {
  current_page: number
  total_pages: number
  per_page: number
  total: number
}

export interface TransactionsResponse {
  data: Transaction[]
  pagination: TransactionPagination
}

export interface TransactionFilters {
  type?: 'deposit' | 'withdrawal' | 'bonus'
  status?: 'pending' | 'completed' | 'failed' | 'cancelled'
  page?: number
  per_page?: number
}
