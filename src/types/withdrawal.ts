// Withdrawal API Types
import type { LaravelPaginatedResponse } from './api'

export interface WithdrawalUser {
  id: number
  tele_id: string
  username: string
  ton_balance: string
  token_balance: string
}

export interface Withdrawal {
  id: number
  user_id: number
  amount: string
  currency: 'TON'
  wallet_address: string
  status: WithdrawalStatus
  notes?: string
  admin_notes?: string
  created_at: string
  updated_at: string
  approved_at?: string | null
  rejected_at?: string | null
  cancelled_at?: string | null
  user?: WithdrawalUser
}

export type WithdrawalStatus = 'pending' | 'approved' | 'rejected' | 'cancelled'

// Request types
export interface CreateWithdrawalRequest {
  amount: number
  wallet_address: string
  notes?: string
}


// Laravel-style pagination response for withdrawals
export type WithdrawalPaginationResponse = LaravelPaginatedResponse<Withdrawal>