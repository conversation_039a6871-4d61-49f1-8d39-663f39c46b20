import { createPinia } from 'pinia'
import { createApp } from 'vue'
import { createAppI18n } from '@/locales'
import { useBootstrapStore } from '@/stores/useBootstrapStore'

import App from './App.vue'
import router from './router/index'
import './style.css'

async function bootstrapApp() {
  try {
    // Create Vue app instance
    const app = createApp(App)

    // Setup Pinia store
    const pinia = createPinia()
    app.use(pinia)

    // Setup router
    app.use(router)

    // Setup i18n
    const i18n = createAppI18n('en')
    app.use(i18n)

    // Initialize bootstrap store and wait for backend data
    const bootstrapStore = useBootstrapStore()
    bootstrapStore.initialize()

    // Wait for router to be ready
    await router.isReady()

    // Mount the app
    app.mount('#app')
  }
  catch (error) {
    console.error('Failed to bootstrap application:', error)
    // Handle critical initialization errors
    document.body.innerHTML = `
      <div style="display: flex; justify-content: center; align-items: center; height: 100vh; font-family: Arial, sans-serif;">
        <div style="text-align: center;">
          <h2>Application Failed to Initialize</h2>
          <p>Please refresh the page to try again.</p>
          <button onclick="location.reload()" style="padding: 10px 20px; margin-top: 10px;">Refresh</button>
        </div>
      </div>
    `
  }
}

bootstrapApp()
