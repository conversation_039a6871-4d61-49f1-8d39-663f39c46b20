import type { ApiResponse } from '@/types/api'
import { createApiClient } from '@/utils/apiClient'

interface TonPaymentInitResponse {
  transaction_hash: string
  message: string
  address: string
  nano_amount_ton: number
}

interface TonPaymentCompleteResponse {
  transaction_hash: string
  order_id: number
  status: string
}

// Boost payment interfaces based on PAYMENT_API.md
interface BoostPaymentInitResponse {
  transaction_hash: string
  message: string
  address: string
  ton_amount: number
}

interface BoostPaymentCompleteResponse {
  transaction_hash: string
  status: string
}

/**
 * Initializes a TON payment transaction.
 * @param productId The ID of the product to purchase
 * @param quantity The quantity of the product
 * @returns Promise<TonPaymentInitResponse> The transaction hash, message, and address
 * @throws Error if the API request fails
 */
export async function initTonPayment(productId: number, quantity: number): Promise<TonPaymentInitResponse> {
  try {
    const apiClient = createApiClient()
    const response = await apiClient.post<ApiResponse<TonPaymentInitResponse>>('/user/ton-payment/init', {
      product_id: productId,
      quantity,
    })

    if (!response.success) {
      throw new Error(response.message || 'Ton payment initialization failed')
    }

    return response.data
  }
  catch (error: any) {
    if (error.response?.data) {
      const errorData = error.response.data

      // Handle validation errors (422 status)
      if (errorData.status === 422) {
        throw new Error(errorData.message || 'Validation error')
      }
    }

    throw new Error('Ton payment initialization failed')
  }
}

/**
 * Completes a TON payment transaction.
 * @param hash The transaction hash from the init response
 * @param productId The ID of the product being purchased
 * @param quantity The quantity of the product
 * @param telegramUsername The user's Telegram username
 * @returns Promise<TonPaymentCompleteResponse> The transaction hash, order ID, and status
 * @throws Error if the API request fails
 */
export async function completeTonPayment(
  hash: string,
  productId: number,
  quantity: number,
  telegramUsername: string,
): Promise<TonPaymentCompleteResponse> {
  try {
    const apiClient = createApiClient()
    const response = await apiClient.post<ApiResponse<TonPaymentCompleteResponse>>('/user/ton-payment/complete', {
      hash,
      product_id: productId,
      quantity,
      telegram_username: telegramUsername,
    })

    if (!response.success) {
      throw new Error(response.message || 'Transaction completion failed')
    }

    return response.data
  }
  catch (error: any) {
    if (error.response?.data) {
      const errorData = error.response.data

      // Handle various error statuses
      if (errorData.status === 404) {
        throw new Error(errorData.message || 'Transaction not found')
      }
      if (errorData.status === 400) {
        throw new Error(errorData.message || 'Transaction validation failed')
      }
      if (errorData.status === 422) {
        throw new Error(errorData.message || 'Validation error')
      }
    }

    throw new Error('Transaction completion failed')
  }
}

/**
 * Cancels a pending TON payment transaction.
 * @param hash The transaction hash to cancel
 * @returns Promise<void> Success confirmation
 * @throws Error if the API request fails
 */
export async function cancelTonPayment(hash: string, message: string): Promise<void> {
  try {
    const apiClient = createApiClient()
    const response = await apiClient.post<ApiResponse<void>>('/user/ton-payment/cancel', {
      hash,
      message,
    })

    if (!response.success) {
      throw new Error(response.message || 'Transaction cancellation failed')
    }
  }
  catch (error: any) {
    if (error.response?.data) {
      const errorData = error.response.data

      // Handle not found errors
      if (errorData.status === 404) {
        throw new Error(errorData.message || 'Transaction not found')
      }
    }

    throw new Error('Transaction cancellation failed')
  }
}

// Boost payment functions based on PAYMENT_API.md

/**
 * Initializes a boost payment transaction.
 * @param tonAmount The amount of TON to pay for the boost
 * @returns Promise<BoostPaymentInitResponse> The transaction hash, message, and address
 * @throws Error if the API request fails
 */
export async function initBoostPayment(tonAmount: number): Promise<BoostPaymentInitResponse> {
  try {
    const apiClient = createApiClient()
    const response = await apiClient.post<ApiResponse<BoostPaymentInitResponse>>('/user/ton-payment/message', {
      ton: tonAmount,
    })

    if (!response.success) {
      throw new Error(response.message || 'Boost payment initialization failed')
    }

    return response.data
  }
  catch (error: any) {
    if (error.response?.data) {
      const errorData = error.response.data

      // Handle validation errors (422 status)
      if (errorData.status === 422) {
        throw new Error(errorData.message || 'Invalid TON amount')
      }
    }

    throw new Error('Boost payment initialization failed')
  }
}

/**
 * Completes a boost payment transaction.
 * @param hash The transaction hash from the init response
 * @returns Promise<BoostPaymentCompleteResponse> The transaction hash and status
 * @throws Error if the API request fails
 */
export async function completeBoostPayment(hash: string): Promise<BoostPaymentCompleteResponse> {
  try {
    const apiClient = createApiClient()
    const response = await apiClient.post<ApiResponse<BoostPaymentCompleteResponse>>('/user/ton-payment/complete', {
      hash,
    })

    if (!response.success) {
      throw new Error(response.message || 'Boost payment completion failed')
    }

    return response.data
  }
  catch (error: any) {
    if (error.response?.data) {
      const errorData = error.response.data

      // Handle various error statuses
      if (errorData.status === 404) {
        throw new Error(errorData.message || 'No pending transaction found')
      }
      if (errorData.status === 400) {
        throw new Error(errorData.message || 'Transaction not found on blockchain or failed')
      }
      if (errorData.status === 422) {
        throw new Error(errorData.message || 'Validation error')
      }
    }

    throw new Error('Boost payment completion failed')
  }
}
