import type { UserTelegram } from '@/types'
import type { ApiResponse } from '@/types/api'
import type { MiningClaimsResponse, SyncUserRequest, SyncUserResponse, SyncUserResult, UserData } from '@/types/user'
import { createApiClient } from '@/utils/apiClient'

/**
 * Syncs a user with the backend API based on Telegram user data
 * Handles both user initialization (new users) and restoration (existing users)
 *
 * @param telegramUser - The Telegram user data
 * @returns Promise<SyncUserResult> - The user data and action from the API response
 * @throws Error if the API request fails or validation errors occur
 */
export async function syncUser(telegramUser: UserTelegram): Promise<SyncUserResult> {
  try {
    // Prepare the request payload according to the API specification
    const requestPayload: SyncUserRequest = {
      telegram_id: telegramUser.id.toString(),
      username: telegramUser.username || undefined,
      name: telegramUser.first_name + (telegramUser.last_name ? ` ${telegramUser.last_name}` : ''),
      language_code: telegramUser.language_code || undefined,
      avatar_url: telegramUser.photo_url || undefined,
    }

    // Make the API request to sync the user
    const apiClient = createApiClient()
    const response = await apiClient.post<ApiResponse<SyncUserResponse>>('/users/sync', requestPayload)

    // Check if the response indicates success
    if (!response.success) {
      throw new Error(response.message || 'User sync failed')
    }

    // Return the user data and action from the successful response
    return {
      user: response.data.user,
      action: response.data.action,
      token: response.data.token,
      token_expires_at: response.data.token_expires_at,
    }
  }
  catch (error: any) {
    // Handle API errors with detailed error messages
    if (error.response?.data) {
      const errorData = error.response.data

      // Handle validation errors (422 status)
      if (errorData.errors) {
        const validationErrors = Object.entries(errorData.errors)
          .map(([field, messages]) => `${field}: ${(messages as string[]).join(', ')}`)
          .join('; ')
        throw new Error(`Validation failed: ${validationErrors}`)
      }

      // Handle other API errors
      throw new Error(errorData.message || 'User sync failed')
    }

    // Handle network or other errors
    throw new Error(error.message || 'Failed to sync user with server')
  }
}

export async function me(): Promise<UserData> {
  try {
    const apiClient = createApiClient()
    const response = await apiClient.get<ApiResponse<UserData>>('/user/me')

    if (!response.success) {
      throw new Error(response.message || 'Failed to fetch user data')
    }

    return response.data
  }
  catch (error: any) {
    throw new Error(error.message || 'Failed to fetch user data')
  }
}

/**
 * Claims pending mining tokens for the authenticated user
 * Adds pending tokens to the user's main balance and resets pending tokens to 0
 *
 * @returns Promise<MiningClaimsResponse> - The claim result with claimed amount and new balance
 * @throws Error if the user doesn't have enough pending tokens or if the API request fails
 */
export async function claimMiningTokens(): Promise<MiningClaimsResponse> {
  try {
    // Make the API request to claim mining tokens
    const apiClient = createApiClient()
    const response = await apiClient.post<ApiResponse<MiningClaimsResponse>>('/user/mining/claimed')

    // Check if the response indicates success
    if (!response.success) {
      throw new Error(response.message || 'Failed to claim mining tokens')
    }

    // Return the claim data from the successful response
    return response.data
  }
  catch (error: any) {
    // Handle API errors with detailed error messages
    if (error.response?.data) {
      const errorData = error.response.data

      // Handle validation errors or insufficient tokens (400 status)
      if (errorData.message) {
        throw new Error(errorData.message)
      }

      // Handle other API errors
      throw new Error('Failed to claim mining tokens')
    }

    // Handle network or other errors
    throw new Error(error.message || 'Failed to claim mining tokens')
  }
}
