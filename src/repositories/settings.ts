import type { ClientSettings, ClientSettingsResponse } from '@/types/settings'
import { createApiClient } from '@/utils/apiClient'

/**
 * Retrieves client settings from the public API
 * This endpoint doesn't require authentication as it only returns non-sensitive configuration
 *
 * @returns Promise<ClientSettings> - The client settings configuration
 * @throws Error if the API request fails
 */
export async function getClientSettings(): Promise<ClientSettings> {
  try {
    // Make the API request to get client settings
    const apiClient = createApiClient()
    const response = await apiClient.get<ClientSettingsResponse>('/public/client-settings')

    // Check if the response indicates success
    if (!response.success) {
      throw new Error('Failed to retrieve client settings')
    }

    // Return the settings data from the successful response
    return response.data
  }
  catch (error: any) {
    // Handle API errors with detailed error messages
    if (error.response?.data) {
      const errorData = error.response.data

      // Handle API errors
      throw new Error(errorData.message || 'Failed to retrieve client settings')
    }

    // Handle network or other errors
    throw new Error(error.message || 'Failed to retrieve client settings from server')
  }
}
