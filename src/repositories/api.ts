import type { AxiosError, AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import type { ApiError, ApiResponse } from '@/types/api'
import axios from 'axios'
import { useUserStore } from '@/stores/userStore'

class ApiClient {
  private client: AxiosInstance

  constructor() {
    console.log(import.meta.env.VITE_API_URL)

    this.client = axios.create({
      baseURL: import.meta.env.VITE_API_URL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json; charset=utf-8',
        'Accept': 'application/json',
      },
    })

    this.setupInterceptors()
  }

  private setupInterceptors() {
    // Request interceptor
    this.client.interceptors.request.use(
      (config) => {
        // Removed unsafe 'date' header - browsers won't allow it
        // This can be handled by the Vite proxy server if needed
        config.headers['start-time'] = new Date().toISOString()

        // Add auth token if available and not expired
        // Get userStore inside the interceptor to avoid singleton issues
        try {
          const userStore = useUserStore()
          if (userStore.isAuthenticated && userStore.accessToken) {
            config.headers.Authorization = `Bearer ${userStore.accessToken}`
          }
        }
        catch {
          // Store might not be available yet, continue without token
          console.warn('User store not available in request interceptor')
        }

        return config
      },
      (error) => {
        return Promise.reject(this.handleError(error))
      },
    )

    // Response interceptor
    this.client.interceptors.response.use(
      (response: AxiosResponse) => {
        return response
      },
      (error: AxiosError) => {
        // Handle token expiration
        if (error.response?.status === 401) {
          try {
            const userStore = useUserStore()
            // Clear expired token data
            userStore.clearAuthData()

            // You might want to redirect to login or emit an event here
            console.warn('Authentication token expired or invalid. User logged out.')
          }
          catch {
            // Store might not be available yet
            console.warn('User store not available in response interceptor')
          }
        }

        return Promise.reject(this.handleError(error))
      },
    )
  }

  private handleError(error: AxiosError): ApiError {
    const apiError: ApiError = {
      message: 'An unexpected error occurred',
      success: false,
      error: 'An unexpected error occurred',
    }

    if (error.response) {
      // Server responded with error status
      apiError.success = false
      // Fix TypeScript error by properly typing the response data
      const responseData = error.response.data as any
      apiError.message = responseData?.message || error.message
    }
    else if (error.request) {
      // Request was made but no response received
      apiError.message = 'Network error: No response from server'
      apiError.success = false
    }
    else {
      // Something else happened
      apiError.message = error.message
    }

    return apiError
  }

  // HTTP methods
  async get<T>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.client.get(url, config)
    return response.data
  }

  async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.client.post(url, data, config)
    return response.data
  }

  async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.client.put(url, data, config)
    return response.data
  }

  async patch<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.client.patch(url, data, config)
    return response.data
  }

  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.client.delete(url, config)
    return response.data
  }

  // Helper method to handle authentication responses
  handleAuthResponse<T extends { token: string, token_expires_at: string, user?: any }>(
    response: ApiResponse<T>,
  ): ApiResponse<T> {
    if (response.success && response.data.token && response.data.token_expires_at) {
      try {
        const userStore = useUserStore()

        // Store the authentication data
        userStore.setAuthData(
          response.data.token,
          response.data.token_expires_at,
          response.data.user,
        )

        // Authentication token updated successfully
      }
      catch {
        console.warn('User store not available for auth response handling')
      }
    }

    return response
  }

  // Method to check if current token is valid
  isTokenValid(): boolean {
    try {
      const userStore = useUserStore()
      return userStore.isAuthenticated
    }
    catch {
      return false
    }
  }

  // Method to get time until token expires (in minutes)
  getTokenExpiryTime(): number {
    try {
      const userStore = useUserStore()
      return Math.floor(userStore.getTimeUntilExpiry() / (1000 * 60))
    }
    catch {
      return 0
    }
  }

  // Method to manually clear authentication
  logout(): void {
    try {
      const userStore = useUserStore()
      userStore.clearAuthData()
      // User logged out successfully
    }
    catch {
      console.warn('User store not available for logout')
    }
  }
}

// Create and export singleton instance
export const apiClient = new ApiClient()
export default apiClient
