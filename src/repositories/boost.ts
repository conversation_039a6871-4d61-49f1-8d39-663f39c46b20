import type { ApiResponse, BoostRequest, BoostResponse, BoostValidationError } from '@/types/api'
import { createApiClient } from '@/utils/apiClient'

/**
 * Purchase mining speed boost with TON payment
 *
 * @param tonAmount - Amount of TON to pay
 * @returns Promise<ApiResponse<BoostResponse>> - Success response with boost details
 * @throws ApiError - When validation fails or boost application fails
 */
export async function purchaseBoost(tonAmount: number): Promise<ApiResponse<BoostResponse>> {
  const request: BoostRequest = {
    ton_amount: tonAmount,
  }

  try {
    const apiClient = createApiClient()
    const response = await apiClient.post<BoostResponse>('/api/user/boost', request)

    if (!response.success) {
      throw new Error(response.message || 'Failed to purchase boost')
    }

    return response
  }
  catch (error: any) {
    // Handle specific API errors
    if (error.response?.status === 400) {
      // Blockchain validation failed
      const errorData = error.response.data as { data: BoostValidationError }
      throw new Error(`Transaction validation failed: ${error.response.data.message}`)
    }

    if (error.response?.status === 500) {
      // Boost application failed
      throw new Error('Transaction confirmed but boost application failed')
    }

    // Re-throw other errors
    throw error
  }
}
