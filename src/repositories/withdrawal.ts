import type { CreateWithdrawalRequest, WithdrawalPaginationResponse } from '@/types/withdrawal'
import type { ApiResponse } from '@/types/api'
import { createApiClient } from '@/utils/apiClient'

/**
 * Creates a new withdrawal request for the authenticated user.
 * @param request The withdrawal request data
 * @returns Promise<{message: string}> Success message
 * @throws Error if the API request fails
 */
export async function createWithdrawal(request: CreateWithdrawalRequest): Promise<{ message: string }> {
  try {
    const apiClient = createApiClient()
    const response = await apiClient.post<ApiResponse<{ message: string }>>('/user/withdrawals', request)

    if (!response.success) {
      throw new Error(response.message || 'Withdrawal request creation failed')
    }

    return response.data
  }
  catch (error: any) {
    if (error.response?.data) {
      const errorData = error.response.data

      // Handle validation errors (400 status)
      if (errorData.status === 400) {
        throw new Error(errorData.message || 'Validation error')
      }
    }

    throw new Error('Withdrawal request creation failed')
  }
}

/**
 * Retrieves withdrawal history for the authenticated user.
 * @param page Page number for pagination (optional, defaults to 1)
 * @returns Promise<WithdrawalPaginationResponse> Paginated withdrawal data
 * @throws Error if the API request fails
 */
export async function getUserWithdrawals(page: number = 1): Promise<WithdrawalPaginationResponse> {
  try {
    const params = page > 1 ? { page: page.toString() } : {}
    const apiClient = createApiClient()
    const response = await apiClient.get<ApiResponse<WithdrawalPaginationResponse>>('/user/withdrawals', { params })

    if (!response.success) {
      throw new Error(response.message || 'Failed to fetch withdrawal history')
    }

    return response.data
  }
  catch (error: any) {
    throw new Error(error.message || 'Failed to fetch withdrawal history')
  }
}

/**
 * Cancels a pending withdrawal request and refunds the amount to user's balance.
 * @param withdrawalId ID of the withdrawal to cancel
 * @returns Promise<{message: string}> Success message
 * @throws Error if the API request fails
 */
export async function cancelWithdrawal(withdrawalId: number): Promise<{ message: string }> {
  try {
    const apiClient = createApiClient()
    const response = await apiClient.post<ApiResponse<{ message: string }>>(`/user/withdrawals/${withdrawalId}/cancel`)

    if (!response.success) {
      throw new Error(response.message || 'Withdrawal cancellation failed')
    }

    return response.data
  }
  catch (error: any) {
    if (error.response?.data) {
      const errorData = error.response.data

      // Handle not found errors
      if (errorData.status === 404) {
        throw new Error(errorData.message || 'Withdrawal not found')
      }
    }

    throw new Error('Withdrawal cancellation failed')
  }
}
