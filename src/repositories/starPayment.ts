import { createApiClient } from '@/utils/apiClient'

interface CreateInvoiceResponse {
  success: boolean
  message: string
  data: {
    transaction_hash: string
    invoice_link: string
  }
}

export async function convertStarToTon(starAmount: number) {
  try {
    const apiClient = createApiClient()
    const response = await apiClient.post<CreateInvoiceResponse>('/star-payment/create-invoice', {
      amount: starAmount,
    })

    return response.data
  }
  catch (error) {
    console.error('Error converting stars to tons:', error)
    throw error
  }
}

export async function getTonReceive(starAmount: number) {
  try {
    const apiClient = createApiClient()
    const response = await apiClient.post<{
      nano_ton: number
    }>('/user/star-to-ton', {
      amount: starAmount,
    })

    return response.nano_ton
  }
  catch (error) {
    console.error('Error getting tons receive:', error)
    throw error
  }
}

export async function verifyStarConversion(transactionHash: string) {
  try {
    const apiClient = createApiClient()
    const response = await apiClient.post<{
      success: boolean
      message: string
      data: {
        status: string
      }
    }>('/star-payment/verify-invoice', {
      transaction_hash: transactionHash,
    })

    if (!response.success) {
      // Throw error to trigger retry mechanism
      throw new Error(`Star conversion verification failed: ${response.message || 'Unknown error'}`)
    }

    return response.success
  }
  catch (error) {
    console.error('Error verifying star conversion:', error)
    throw error
  }
}
