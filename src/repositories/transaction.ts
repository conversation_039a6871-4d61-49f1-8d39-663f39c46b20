import type { ApiResponse, TransactionFilters, TransactionsResponse } from '@/types/api'
import { createApiClient } from '@/utils/apiClient'

/**
 * Fetches user transactions with optional filtering and pagination
 *
 * @param filters - Optional filters for transaction type, status, and pagination
 * @returns Promise<ApiResponse<TransactionsResponse>> - The transactions data with pagination info
 * @throws Error if the API request fails or user is not authenticated
 */
export async function getUserTransactions(filters?: TransactionFilters): Promise<TransactionsResponse> {
  try {
    // Build query parameters from filters
    const params = new URLSearchParams()

    if (filters?.type) {
      params.append('type', filters.type)
    }

    if (filters?.page) {
      params.append('page', filters.page.toString())
    }

    if (filters?.per_page) {
      params.append('per_page', filters.per_page.toString())
    }

    // Construct the URL with query parameters
    const url = `/user/transactions${params.toString() ? `?${params.toString()}` : ''}`

    // Make the API request to get user transactions
    const apiClient = createApiClient()
    const response = await apiClient.get<ApiResponse<TransactionsResponse>>(url)

    return response.data
  }
  catch (error: any) {
    // Handle API errors with detailed error messages
    if (error.response?.data) {
      const errorData = error.response.data

      // Handle authentication errors (401 status)
      if (error.response.status === 401) {
        throw new Error('Authentication required to access transactions')
      }

      // Handle validation errors (422 status)
      if (errorData.errors) {
        const validationErrors = Object.entries(errorData.errors)
          .map(([field, messages]) => `${field}: ${(messages as string[]).join(', ')}`)
          .join('; ')
        throw new Error(`Validation failed: ${validationErrors}`)
      }

      // Handle other API errors
      throw new Error(errorData.message || 'Failed to fetch transactions')
    }

    // Handle network or other errors
    throw new Error(error.message || 'Failed to fetch transactions from server')
  }
}
