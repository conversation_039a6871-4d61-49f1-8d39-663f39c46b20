import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useUserStore } from '@/stores/userStore'
import { storeToRefs } from 'pinia'

const GAS_PER_SECOND = 1_000_000

interface UserMiningData {
  speed: number
  lastCheckpoint: Date
  boostExpiredAt?: Date
}

function calculatePendingTokens(user: UserMiningData, endTime?: Date): number {
  const now = endTime ?? new Date()
  const lastCheckpoint = user.lastCheckpoint ?? now
  
  const secondsElapsed = Math.floor((now.getTime() - lastCheckpoint.getTime()) / 1000)
  
  if (secondsElapsed <= 0) {
    return 0
  }
  
  const coinsMined = (user.speed * secondsElapsed) / GAS_PER_SECOND
  
  return coinsMined
}

export function useMiningStats() {
  const { user } = storeToRefs(useUserStore())
  const currentTokens = ref(0)
  const miningSpeed = ref(0)
  const intervalId = ref<number | null>(null)
  const lastApiSync = ref<Date | null>(null)
  
  // Real mining data from user store
  const miningData = computed((): UserMiningData | null => {
    if (!user.value) return null
    return {
      speed: user.value.speed,
      lastCheckpoint: new Date(user.value.last_checkpoint),
      boostExpiredAt: undefined
    }
  })
  
  const baseBalance = computed(() => {
    if (!user.value) return 0
    return parseFloat(user.value.balance_token) || 0
  })
  
  const pendingTokens = computed(() => {
    if (!user.value) return 0
    return parseFloat(user.value.pending_token) || 0
  })
  
  // Total tokens = base balance + pending tokens (from API) + additional mining since last API sync
  const totalTokens = computed(() => {
    return baseBalance.value + pendingTokens.value + currentTokens.value
  })
  
  const tokensPerSecond = computed(() => {
    if (!miningData.value) return 0
    return miningData.value.speed / GAS_PER_SECOND
  })
  
  function updateMiningStats() {
    if (!miningData.value) {
      currentTokens.value = 0
      miningSpeed.value = 0
      return
    }
    
    // Calculate additional mining progress since last API sync
    if (lastApiSync.value) {
      const additionalMiningData = {
        speed: miningData.value.speed,
        lastCheckpoint: lastApiSync.value,
        boostExpiredAt: undefined
      }
      currentTokens.value = calculatePendingTokens(additionalMiningData)
    } else {
      currentTokens.value = 0
    }
    
    miningSpeed.value = tokensPerSecond.value
  }
  
  function startMining() {
    // Set the API sync time to now when starting mining
    lastApiSync.value = new Date()
    updateMiningStats()
    
    // Update every second
    intervalId.value = window.setInterval(() => {
      updateMiningStats()
    }, 1000)
  }
  
  function stopMining() {
    if (intervalId.value) {
      clearInterval(intervalId.value)
      intervalId.value = null
    }
  }
  
  function resetApiSync() {
    lastApiSync.value = new Date()
    currentTokens.value = 0
  }
  
  onMounted(() => {
    startMining()
  })
  
  onUnmounted(() => {
    stopMining()
  })
  
  return {
    currentTokens,
    totalTokens,
    tokensPerSecond,
    miningSpeed,
    pendingTokens,
    baseBalance,
    startMining,
    stopMining,
    updateMiningStats,
    resetApiSync
  }
}