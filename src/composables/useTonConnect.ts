import type { Locales } from '@tonconnect/ui'
import { beginCell } from '@ton/core'
import { THEME, TonConnectUI } from '@tonconnect/ui'
import { computed, onMounted, ref } from 'vue'
import { useUserStore } from '../stores/userStore'

function buildPayload(text: string) {
  // ‼️ 32-bit opcode (0) để smart-contract dễ phân loại, có thể đổi tuỳ ý
  return beginCell()
    .storeUint(0, 32) // opcode
    .storeStringTail(text) // đẩy UTF-8 tail
    .endCell()
    .toBoc({ idx: false }) // BOC không kèm index
    .toString('base64') // TonConnect yêu cầu base64
}

export function useTonConnect() {
  const tonConnectUI = ref<TonConnectUI | null>(null)
  const user = computed(() => useUserStore().user)
  const isConnected = computed(() => tonConnectUI.value?.connected)
  const address = computed(() => tonConnectUI.value?.account?.address)

  const initializeTonConnect = async () => {
    const options = {
      manifestUrl: `${import.meta.env.VITE_APP_URL}/tonconnect-manifest.json`,
    }

    tonConnectUI.value = new TonConnectUI(options)

    tonConnectUI.value.uiOptions = {
      language: user.value?.language_code as Locales,
      uiPreferences: {
        theme: THEME.LIGHT,
      },
    }

    try {
      await tonConnectUI.value.connectionRestored
    }
    catch (error) {
      console.error('Failed to initialize TonConnect:', error)
    }
  }

  const connectWallet = (cb?: () => void) => {
    if (!tonConnectUI.value || tonConnectUI.value.connected) {
      return cb?.()
    }

    tonConnectUI.value!.openModal()

    let unsubscribeModal: () => void
    const unsubscribeStatus = tonConnectUI.value!.onStatusChange(
      (wallet) => {
        if (wallet) {
          unsubscribeStatus()
          unsubscribeModal()
          return cb?.()
        }
      },
    )

    unsubscribeModal = tonConnectUI.value!.onModalStateChange((modalState) => {
      if (modalState.status === 'closed') {
        unsubscribeStatus()
        unsubscribeModal()
        if (!tonConnectUI.value?.connected)
          console.error('Connection was cancelled.')
        else
          console.log('Connection successful.')
      }
    })
  }

  const sendTransaction = async (address: string, message: string, nanoAmountTon: number) => {
    if (!tonConnectUI.value)
      return

    if (!tonConnectUI.value.connected) {
      await connectWallet()
    }

    const payload = buildPayload(message)
    const transaction = {
      validUntil: Math.floor(Date.now() / 1000) + 30,
      messages: [
        {
          address,
          amount: nanoAmountTon.toString(),
          payload,
        },
      ],
    }

    return await tonConnectUI.value?.sendTransaction(transaction)
  }

  const disconnectWallet = async () => {
    if (!tonConnectUI.value)
      return
    try {
      await tonConnectUI.value.disconnect()
    }
    catch (error) {
      console.error('Failed to disconnect wallet:', error)
    }
  }

  onMounted(() => {
    initializeTonConnect()
  })

  return {
    tonConnectUI,
    user,
    initializeTonConnect,
    connectWallet,
    disconnectWallet,
    isConnected,
    address,
    sendTransaction,
  }
}
