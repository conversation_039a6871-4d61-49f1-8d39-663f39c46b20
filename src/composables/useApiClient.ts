import type { AxiosError, AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import type { ApiError, ApiResponse } from '@/types/api'
import axios from 'axios'
import { useUserStore } from '@/stores/userStore'

export function useApiClient() {
  const userStore = useUserStore()

  // Create axios instance
  const client: AxiosInstance = axios.create({
    baseURL: import.meta.env.VITE_API_URL,
    timeout: 10000,
    headers: {
      'Content-Type': 'application/json; charset=utf-8',
      'Accept': 'application/json',
    },
  })

  // Setup interceptors
  function setupInterceptors() {
    // Request interceptor
    client.interceptors.request.use(
      (config) => {
        // Add timestamp header
        config.headers['start-time'] = new Date().toISOString()

        // Add auth token if available and not expired
        if (userStore.isAuthenticated && userStore.accessToken) {
          config.headers.Authorization = `Bearer ${userStore.accessToken}`
        }

        return config
      },
      (error) => {
        return Promise.reject(handleError(error))
      },
    )

    // Response interceptor
    client.interceptors.response.use(
      (response: AxiosResponse) => {
        return response
      },
      (error: AxiosError) => {
        // Handle token expiration
        if (error.response?.status === 401) {
          // Clear expired token data
          userStore.clearAuthData()
          console.warn('Authentication token expired or invalid. User logged out.')
        }
        
        return Promise.reject(handleError(error))
      },
    )
  }

  // Error handler
  function handleError(error: AxiosError): ApiError {
    const apiError: ApiError = {
      message: 'An unexpected error occurred',
      success: false,
      error: 'An unexpected error occurred',
    }

    if (error.response) {
      // Server responded with error status
      apiError.success = false
      const responseData = error.response.data as any
      apiError.message = responseData?.message || error.message
    }
    else if (error.request) {
      // Request was made but no response received
      apiError.message = 'Network error: No response from server'
      apiError.success = false
    }
    else {
      // Something else happened
      apiError.message = error.message
    }

    return apiError
  }

  // Initialize interceptors
  setupInterceptors()

  // HTTP methods
  async function get<T>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await client.get(url, config)
    return response.data
  }

  async function post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await client.post(url, data, config)
    return response.data
  }

  async function put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await client.put(url, data, config)
    return response.data
  }

  async function patch<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await client.patch(url, data, config)
    return response.data
  }

  async function del<T>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await client.delete(url, config)
    return response.data
  }

  // Helper method to handle authentication responses
  function handleAuthResponse<T extends { token: string, token_expires_at: string, user?: any }>(
    response: ApiResponse<T>,
  ): ApiResponse<T> {
    if (response.success && response.data.token && response.data.token_expires_at) {
      // Store the authentication data
      userStore.setAuthData(
        response.data.token,
        response.data.token_expires_at,
        response.data.user,
      )
    }

    return response
  }

  // Method to check if current token is valid
  function isTokenValid(): boolean {
    return userStore.isAuthenticated
  }

  // Method to get time until token expires (in minutes)
  function getTokenExpiryTime(): number {
    return Math.floor(userStore.getTimeUntilExpiry() / (1000 * 60))
  }

  // Method to manually clear authentication
  function logout(): void {
    userStore.clearAuthData()
  }

  return {
    // HTTP methods
    get,
    post,
    put,
    patch,
    delete: del,
    
    // Auth helpers
    handleAuthResponse,
    isTokenValid,
    getTokenExpiryTime,
    logout,
    
    // Direct access to axios instance if needed
    client,
  }
}
