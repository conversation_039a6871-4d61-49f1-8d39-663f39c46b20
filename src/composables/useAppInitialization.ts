import { ref } from 'vue'
import { useTelegramApp } from './useTelegramApp'

export function useTelegramInitialization() {
  const isTelegramInitialized = ref(false)
  const isTelegramLoading = ref(true)
  const { initTelegramWebApp, platform } = useTelegramApp()

  async function initializeTelegram() {
    try {
      isTelegramLoading.value = true
      await initTelegramWebApp()
      isTelegramInitialized.value = isAllowedPlatform(platform.value) || import.meta.env.VITE_ENV === 'development'
    }
    catch (error) {
      console.error('Failed to initialize Telegram WebApp:', error)
    }
    finally {
      isTelegramLoading.value = false
    }
  }

  return {
    isTelegramInitialized,
    isTelegramLoading,
    initializeTelegram,
  }
}

function isAllowedPlatform(platform: string) {
  return platform === 'android' || platform === 'ios'
}
