import type { SyncUserResult } from '@/types/user'
import { ref } from 'vue'
import { applyUserLanguage } from '@/locales'
import { syncUser } from '@/repositories/user'
import { useUserStore } from '@/stores/userStore'

export function useTelegramApp() {
  const platform = ref<string>('')
  const version = ref<string>('')
  const colorScheme = ref<string>('')
  const isInTelegram = ref<boolean>(false)
  const { setUser, setAccessToken } = useUserStore()

  async function initTelegramWebApp() {
    let result: SyncUserResult | null = null

    if (window.Telegram?.WebApp) {
      const tg = window.Telegram.WebApp

      platform.value = tg.platform
      version.value = tg.version
      colorScheme.value = tg.colorScheme
      isInTelegram.value = true

      if (tg.initDataUnsafe?.user) {
        result = await syncUser(tg.initDataUnsafe.user)
      }
      else if (isLocalHost()) {
        result = await syncUser(buildFakeUser())
      }

      await tg.ready()
      await tg.expand()
    }
    else {
      result = await syncUser(buildFakeUser())
    }

    if (result) {
      setUser(result.user)
      setAccessToken(result.token)
      // Switch i18n to the user's language_code with fallback to 'en'
      applyUserLanguage(result.user.language_code)
    }
  }

  return {
    initTelegramWebApp,

    isInTelegram,
    platform,
  }
}

function buildFakeUser() {
  return {
    id: 123456789,
    first_name: 'John',
    last_name: 'Doe',
    username: 'johndoe',
    language_code: 'en',
    is_premium: false,
    photo_url: 'https://i.pinimg.com/736x/8f/ec/d4/8fecd44b9fd9878ffa4655aa03814ac4.jpg',
  }
}

function isLocalHost() {
  return window.location.hostname === 'localhost'
}
