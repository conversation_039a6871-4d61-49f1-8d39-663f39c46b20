import type { MaybeRefOrGetter } from 'vue'
import { computed, ref, toValue } from 'vue'

interface CountUpOptions {
  duration?: number
  decimals?: number
  separator?: string
  prefix?: string
  suffix?: string
  useGrouping?: boolean
  continuous?: boolean
  increment?: number
  interval?: number
}

export function useCountUp(
  target: MaybeRefOrGetter<number>,
  options: MaybeRefOrGetter<CountUpOptions> = {},
) {
  const currentValue = ref(0)
  const isAnimating = ref(false)

  const formattedValue = computed(() => {
    const opts = toValue(options)
    const value = currentValue.value

    // Format number with options
    let formatted = value.toLocaleString('en-US', {
      minimumFractionDigits: opts.decimals || 0,
      maximumFractionDigits: opts.decimals || 0,
      useGrouping: opts.useGrouping ?? true,
    })

    // Apply custom separator if provided
    if (opts.separator && opts.separator !== ',') {
      formatted = formatted.replace(/,/g, opts.separator)
    }

    // Add prefix and suffix
    if (opts.prefix)
      formatted = opts.prefix + formatted
    if (opts.suffix)
      formatted = formatted + opts.suffix

    return formatted
  })

  function startAnimation() {
    const opts = toValue(options)
    
    // Stop any existing animation or continuous increment
    stopAnimation()
    
    if (opts.continuous) {
      startContinuousIncrement()
    } else {
      startTargetAnimation()
    }
  }
  
  function startTargetAnimation() {
    const targetValue = toValue(target)
    const opts = toValue(options)
    const duration = opts.duration || 2000
    const startValue = currentValue.value
    const difference = targetValue - startValue

    if (difference === 0)
      return

    isAnimating.value = true
    const startTime = performance.now()

    function animate(currentTime: number) {
      const elapsed = currentTime - startTime
      const progress = Math.min(elapsed / duration, 1)

      // Easing function (ease-out)
      const easeOut = 1 - Math.pow(1 - progress, 3)

      currentValue.value = startValue + (difference * easeOut)

      if (progress < 1) {
        requestAnimationFrame(animate)
      }
      else {
        currentValue.value = targetValue
        isAnimating.value = false
      }
    }

    requestAnimationFrame(animate)
  }
  
  function startContinuousIncrement() {
    isAnimating.value = true
    
    function animateToNextIncrement() {
      // Get current options values for reactivity
      const opts = toValue(options)
      const increment = opts.increment || 0
      const interval = opts.interval || 1000
      
      if (increment === 0 || !isAnimating.value) return
      
      const startValue = currentValue.value
      const targetValue = startValue + increment
      const startTime = performance.now()
      
      function animate(currentTime: number) {
        if (!isAnimating.value) return
        
        const elapsed = currentTime - startTime
        const progress = Math.min(elapsed / interval, 1)
        
        // Smooth easing function
        const easeOut = 1 - Math.pow(1 - progress, 3)
        
        currentValue.value = startValue + (increment * easeOut)
        
        if (progress < 1) {
          requestAnimationFrame(animate)
        } else {
          currentValue.value = targetValue
          // Continue to next increment
          if (isAnimating.value) {
            animateToNextIncrement()
          }
        }
      }
      
      requestAnimationFrame(animate)
    }
    
    // Start the continuous animation
    animateToNextIncrement()
  }
  
  function stopAnimation() {
    isAnimating.value = false
  }

  function reset() {
    stopAnimation()
    currentValue.value = 0
  }

  function setValue(value: number) {
    currentValue.value = value
  }

  return {
    currentValue,
    formattedValue,
    isAnimating,
    startAnimation,
    stopAnimation,
    reset,
    setValue,
  }
}
