import { readonly, ref } from 'vue'

interface DialogState {
  isOpen: boolean
  title: string
  message: string
  type: 'success' | 'error'
}

const dialogState = ref<DialogState>({
  isOpen: false,
  title: '',
  message: '',
  type: 'success',
})

export function useGlobalDialog() {
  const showSuccess = (title: string, message: string = '') => {
    dialogState.value = {
      isOpen: true,
      title,
      message,
      type: 'success',
    }
  }

  const showError = (title: string, message: string = '') => {
    dialogState.value = {
      isOpen: true,
      title,
      message,
      type: 'error',
    }
  }

  const closeDialog = () => {
    dialogState.value.isOpen = false
  }

  return {
    dialogState: readonly(dialogState),
    showSuccess,
    showError,
    closeDialog,
  }
}
