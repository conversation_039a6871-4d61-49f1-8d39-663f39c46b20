import type { Tarrif } from '@/types'

export const TARRIFS = [
  {
    name: 'Plus',
    price: 1.49,
    features: [
      'Ads removal',
      'Premium status in the leaderboard',
      'Limited miners 12 hours presale',
    ],
    variant: 'normal',
    image: '/images/plus.png',
  },
  {
    name: 'Pro',
    price: 2.99,
    features: [
      'Ads removal',
      'Premium status in the leaderboard',
      'Limited miners 12 hours presale',
      'Income collection notifications in TG',
      'Priority payouts',
    ],
    variant: 'pro',
    image: '/images/pro.png',
  },
] as Tarrif[]
