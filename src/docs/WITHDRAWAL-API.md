# Withdrawal API Documentation

## Overview
The Withdrawal API provides endpoints for managing withdrawal requests in the cryptocurrency mining application. It handles withdrawal creation, listing, cancellation, and administrative approval/rejection processes.

## Base URL
```
/api/
```

## Authentication
- **User endpoints**: Require user authentication (UserAuth middleware)

## Endpoints

### 1. Create Withdrawal Request
**POST** `/user/withdrawals`

Creates a new withdrawal request for the authenticated user.

#### Request Body
```json
{
  "amount": 0.5,
  "wallet_address": "******************************************",
  "notes": "Optional withdrawal notes"
}
```

#### Request Parameters
| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `amount` | decimal | Yes | Withdrawal amount (minimum: 0.00000001) |
| `wallet_address` | string | Yes | Destination wallet address (max: 255 chars) |
| `notes` | string | No | Optional notes for the withdrawal (max: 1000 chars) |

#### Response
**Success (200)**
```json
{
  "success": true,
  "message": "Withdrawal request created successfully"
}
```

**Error Responses**
- **400 Bad Request**: Validation failed or insufficient balance
- **500 Internal Server Error**: Server error during withdrawal creation

#### Notes
- Withdrawal amount is deducted from user's `ton_balance` immediately
- Withdrawal status is set to `PENDING` upon creation
- Currency is automatically set to `TON`

---

### 2. Get User Withdrawals
**GET** `/user/withdrawals`

Retrieves withdrawal history for the authenticated user.

#### Request Parameters
None

#### Response
**Success (200)**
```json
{
  "success": true,
  "data": {
    "current_page": 1,
    "data": [
      {
        "id": 1,
        "user_id": 123,
        "amount": "0.50000000",
        "currency": "TON",
        "wallet_address": "******************************************",
        "status": "pending",
        "notes": "Test withdrawal",
        "admin_notes": null,
        "created_at": "2025-07-14T10:30:00.000000Z",
        "updated_at": "2025-07-14T10:30:00.000000Z",
        "approved_at": null,
        "rejected_at": null,
        "cancelled_at": null
      }
    ],
    "first_page_url": "http://localhost:8000/api/user/withdrawals?page=1",
    "from": 1,
    "last_page": 1,
    "last_page_url": "http://localhost:8000/api/user/withdrawals?page=1",
    "next_page_url": null,
    "path": "http://localhost:8000/api/user/withdrawals",
    "per_page": 20,
    "prev_page_url": null,
    "to": 1,
    "total": 1
  }
}
```

#### Notes
- Returns paginated results (20 items per page)
- Ordered by creation date (newest first)
- Only shows withdrawals for the authenticated user

---

### 3. Cancel Withdrawal
**POST** `/user/withdrawals/{id}/cancel`

Cancels a pending withdrawal request and refunds the amount to user's balance.

#### URL Parameters
| Parameter | Type | Description |
|-----------|------|-------------|
| `id` | integer | Withdrawal ID to cancel |

#### Request Body
None

#### Response
**Success (200)**
```json
{
  "success": true,
  "message": "Withdrawal cancelled successfully"
}
```

**Error Responses**
- **404 Not Found**: Withdrawal not found or cannot be cancelled
- **500 Internal Server Error**: Server error during cancellation

#### Notes
- Only `PENDING` withdrawals can be cancelled
- Users can only cancel their own withdrawals
- Withdrawal amount is refunded to user's `ton_balance`
- Withdrawal status is set to `CANCELLED`
- `cancelled_at` timestamp is set

---

## Data Models

### Withdrawal Object
```json
{
  "id": 1,
  "user_id": 123,
  "amount": "0.50000000",
  "currency": "TON",
  "wallet_address": "******************************************",
  "status": "pending",
  "notes": "User notes",
  "admin_notes": "Admin notes",
  "created_at": "2025-07-14T10:30:00.000000Z",
  "updated_at": "2025-07-14T10:30:00.000000Z",
  "approved_at": null,
  "rejected_at": null,
  "cancelled_at": null,
  "user": {
    "id": 123,
    "tele_id": "987654321",
    "username": "testuser",
    "ton_balance": "4.50000000",
    "token_balance": "1000.00000000"
  }
}
```

### Withdrawal Status Enum
- `pending` - Initial status when withdrawal is created
- `approved` - Approved by admin and processed
- `rejected` - Rejected by admin, amount refunded
- `cancelled` - Cancelled by user, amount refunded

### Currency Enum
- `TON` - The Open Network cryptocurrency

---

## Error Handling

All endpoints return consistent error responses:

```json
{
  "success": false,
  "message": "Error description",
  "errors": {
    "field_name": ["Validation error message"]
  }
}
```

### Common HTTP Status Codes
- `200` - Success
- `400` - Bad Request (validation errors, insufficient balance)
- `404` - Not Found (withdrawal not found)
- `500` - Internal Server Error

---

## Balance Management

### User Balance Impact
- **Create Withdrawal**: Deducts amount from `ton_balance` immediately
- **Cancel Withdrawal**: Refunds amount to `ton_balance`
- **Reject Withdrawal**: Refunds amount to `ton_balance`
- **Approve Withdrawal**: No balance change (amount already deducted)

### Transaction Safety
All balance modifications are wrapped in database transactions to ensure data consistency.

---

## Usage Examples

### Create a withdrawal request
```bash
curl -X POST "http://localhost:8000/api/user/withdrawals" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_USER_TOKEN" \
  -d '{
    "amount": 0.5,
    "wallet_address": "******************************************",
    "notes": "Test withdrawal"
  }'
```

### Get user withdrawals
```bash
curl -X GET "http://localhost:8000/api/user/withdrawals" \
  -H "Authorization: Bearer YOUR_USER_TOKEN"
```

### Cancel a withdrawal
```bash
curl -X POST "http://localhost:8000/api/user/withdrawals/1/cancel" \
  -H "Authorization: Bearer YOUR_USER_TOKEN"
```
