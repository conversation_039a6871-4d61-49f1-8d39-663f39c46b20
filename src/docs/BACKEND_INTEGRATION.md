# TON Connect Backend Integration Guide

This document provides a comprehensive guide for implementing secure TON Connect payment processing with backend integration.

## 🚨 **CRITICAL SECURITY WARNING**

**NEVER trust client-side transaction data without blockchain verification!**

The most critical security requirement is to **always verify transactions on the TON blockchain** before processing any orders or granting access. Trusting client-provided BOC data without verification would allow users to fake payments.

## 🏗️ Architecture Overview

```
Frontend (Vue 3)          Backend API              TON Blockchain
     │                         │                         │
     │  1. Prepare Transaction  │                         │
     ├─────────────────────────>│                         │
     │  2. Return TX Data       │                         │
     │<─────────────────────────┤                         │
     │                         │                         │
     │  3. Send TON Transaction │                         │
     ├─────────────────────────────────────────────────────>│
     │  4. Transaction Result   │                         │
     │<─────────────────────────────────────────────────────┤
     │                         │                         │
     │  5. Confirm with Backend │                         │
     ├─────────────────────────>│                         │
     │  6. Process Order        │                         │
     │<─────────────────────────┤                         │
     │                         │  7. Webhook (Optional)   │
     │                         │<─────────────────────────┤
```

## 🚀 Implementation Steps

### 1. Backend API Endpoints

Create these endpoints in your backend:

#### **POST /api/payments/prepare**
```typescript
interface PreparePaymentRequest {
  productId: string
  quantity: number
  userId?: string
}

interface PreparePaymentResponse {
  transactionId: string
  destinationAddress: string // Your merchant wallet
  amount: string // Amount in TON
  description: string
  validUntil: number // Unix timestamp
  metadata: {
    productId: string
    orderId: string
    userId: string
    quantity: number
  }
}
```

**Example Implementation (Node.js/Express):**
```typescript
app.post('/api/payments/prepare', async (req, res) => {
  const { productId, quantity, userId } = req.body

  // 1. Validate product exists
  const product = await getProduct(productId)
  if (!product) {
    return res.status(404).json({ error: 'Product not found' })
  }

  // 2. Calculate total amount
  const totalAmount = product.price * quantity

  // 3. Create transaction record in database
  const transaction = await db.transactions.create({
    productId,
    quantity,
    userId,
    amount: totalAmount,
    status: 'pending',
    validUntil: new Date(Date.now() + 5 * 60 * 1000), // 5 minutes
    merchantAddress: process.env.MERCHANT_WALLET_ADDRESS
  })

  // 4. Return transaction data
  res.json({
    transactionId: transaction.id,
    destinationAddress: transaction.merchantAddress,
    amount: totalAmount.toString(),
    description: `${product.name} x${quantity}`,
    validUntil: Math.floor(transaction.validUntil.getTime() / 1000),
    metadata: {
      productId,
      orderId: transaction.orderId,
      userId: userId || 'anonymous',
      quantity
    }
  })
})
```

#### **POST /api/payments/confirm**
```typescript
interface ConfirmPaymentRequest {
  transactionId: string
  tonTransaction: {
    boc: string // Transaction BOC from TON Connect
  }
}

interface ConfirmPaymentResponse {
  status: 'confirmed' | 'failed' | 'pending'
  orderId: string
  txHash?: string
}
```

**Example Implementation:**
```typescript
app.post('/api/payments/confirm', async (req, res) => {
  const { transactionId, boc } = req.body

  try {
    // 1. Get the expected transaction from database
    const expectedTx = await getTransactionFromDB(transactionId)

    // 2. Check expiry
    if (new Date() > expectedTx.expires_at) {
      await db.transactions.update(transactionId, {
        status: 'expired',
        failureReason: 'Transaction expired'
      })
      return res.status(400).json({ error: 'Transaction expired' })
    }

    // 3. 🔐 CRITICAL: Verify on TON blockchain
    const tonClient = new TonClient({
      endpoint: process.env.TON_API_ENDPOINT,
      apiKey: process.env.TON_API_KEY
    })

    // Parse BOC and extract transaction details
    const cell = Cell.fromBoc(Buffer.from(boc, 'base64'))[0]
    const transaction = parseTransaction(cell)

    // Verify transaction exists on blockchain
    const onChainTx = await tonClient.getTransaction(
      Address.parse(transaction.address),
      transaction.lt.toString()
    )

    if (!onChainTx) {
      throw new Error('Transaction not found on blockchain')
    }

    // 4. Validate transaction details
    const inMessage = onChainTx.inMessage
    if (!inMessage || inMessage.dest.toString() !== expectedTx.destination_address) {
      throw new Error('Invalid destination address')
    }

    const actualAmount = fromNano(inMessage.value.coins)
    if (actualAmount !== expectedTx.expected_amount.toString()) {
      throw new Error(`Amount mismatch. Expected: ${expectedTx.expected_amount}, Got: ${actualAmount}`)
    }

    // 5. Check for sufficient confirmations
    const currentBlock = await tonClient.getMasterchainInfo()
    const confirmations = currentBlock.last.seqno - onChainTx.now

    if (confirmations < 1) {
      return res.status(202).json({
        status: 'pending',
        message: 'Waiting for confirmations',
        confirmations
      })
    }

    // 6. Prevent replay attacks - check if TX already processed
    const [existingTx] = await db.transactions.findMany({
      where: {
        ton_tx_hash: onChainTx.hash,
        id: { not: transactionId }
      }
    })

    if (existingTx) {
      throw new Error('Transaction already processed')
    }

    // 7. Update transaction status
    await db.transactions.update(transactionId, {
      status: 'confirmed',
      ton_tx_hash: onChainTx.hash,
      ton_tx_lt: onChainTx.lt.toString(),
      confirmations,
      confirmedAt: new Date(),
      updatedAt: new Date()
    })

    // 8. Process the order
    await processOrder(expectedTx)

    res.json({
      status: 'confirmed',
      txHash: onChainTx.hash,
      confirmations,
      orderId: expectedTx.order_id
    })
  }
  catch (error) {
    // Mark transaction as failed
    await db.transactions.update(transactionId, {
      status: 'failed',
      failureReason: error.message
    })
    res.status(400).json({ error: error.message })
  }
})
```

#### **GET /api/payments/:transactionId/status**
```typescript
app.get('/api/payments/:transactionId/status', async (req, res) => {
  const transaction = await db.transactions.findById(req.params.transactionId)

  if (!transaction) {
    return res.status(404).json({ error: 'Transaction not found' })
  }

  res.json({
    id: transaction.id,
    status: transaction.status,
    txHash: transaction.ton_tx_hash,
    confirmations: transaction.confirmations,
    createdAt: transaction.created_at,
    updatedAt: transaction.updated_at
  })
})
```

### 2. Frontend Integration

The frontend code is already implemented in `src/components/TonPayment.vue`. Key points:

1. **Prepare Transaction**: Call `/api/payments/prepare` before showing TON Connect
2. **Send TON Transaction**: Use the prepared transaction data
3. **Confirm with Backend**: Send the result to `/api/payments/confirm`
4. **Handle Status**: Poll or websocket for status updates

### 3. Database Schema

```sql
CREATE TABLE transactions (
  id VARCHAR(255) PRIMARY KEY,
  order_id VARCHAR(255) UNIQUE NOT NULL,
  product_id VARCHAR(255) NOT NULL,
  user_id VARCHAR(255),
  quantity INTEGER NOT NULL,
  amount DECIMAL(18, 9) NOT NULL,
  merchant_address VARCHAR(255) NOT NULL,
  status ENUM('pending', 'confirmed', 'failed', 'expired') DEFAULT 'pending',
  ton_tx_hash VARCHAR(255),
  ton_tx_lt BIGINT,
  confirmations INTEGER DEFAULT 0,
  valid_until TIMESTAMP NOT NULL,
  failure_reason TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

CREATE TABLE products (
  id VARCHAR(255) PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  price DECIMAL(18, 9) NOT NULL,
  active BOOLEAN DEFAULT TRUE
);
```

### 4. Security Considerations

1. **Validate All Inputs**: Sanitize and validate all user inputs
2. **Rate Limiting**: Implement rate limiting on payment endpoints
3. **Transaction Expiry**: Always set and check transaction expiry
4. **Blockchain Verification**: Always verify transactions on the actual TON blockchain
5. **Idempotency**: Make endpoints idempotent to handle retries
6. **HTTPS Only**: Always use HTTPS for API endpoints

### 5. Error Handling

```typescript
// Common error responses
const PaymentErrors = {
  PRODUCT_NOT_FOUND: { code: 'PRODUCT_NOT_FOUND', message: 'Product not found' },
  TRANSACTION_EXPIRED: { code: 'TRANSACTION_EXPIRED', message: 'Transaction has expired' },
  INVALID_AMOUNT: { code: 'INVALID_AMOUNT', message: 'Transaction amount does not match' },
  BLOCKCHAIN_ERROR: { code: 'BLOCKCHAIN_ERROR', message: 'Failed to verify on blockchain' },
  DUPLICATE_TRANSACTION: { code: 'DUPLICATE_TRANSACTION', message: 'Transaction already processed' }
}
```

### 6. Webhook Integration (Optional)

For real-time updates, implement TON blockchain webhooks:

```typescript
app.post('/api/webhooks/ton', async (req, res) => {
  const { txHash, status, confirmations } = req.body

  // Verify webhook signature
  if (!verifyWebhookSignature(req)) {
    return res.status(401).json({ error: 'Invalid signature' })
  }

  // Update transaction status
  await db.transactions.updateByTxHash(txHash, {
    status,
    confirmations,
    updatedAt: new Date()
  })

  // Notify frontend via WebSocket
  websocket.emit('transaction-update', { txHash, status, confirmations })

  res.json({ success: true })
})
```

## 🔧 Environment Variables

```env
# Backend
MERCHANT_WALLET_ADDRESS=EQABa48hjKzg09hN_HjxOic7r8T1PleIy1dRd8NvZ3922MP0
TON_API_KEY=your_ton_api_key
TON_NETWORK=mainnet  # or testnet
DATABASE_URL=postgresql://user:pass@localhost/db

# Frontend
VITE_API_BASE_URL=https://your-api.com
VITE_APP_URL=https://your-app.com
```

## 📦 Required Dependencies

**Backend (Node.js):**
```json
{
  "dependencies": {
    "@ton/ton": "^13.0.0",
    "@ton/crypto": "^3.2.0",
    "express": "^4.18.0",
    "prisma": "^5.0.0"
  }
}
```

**Frontend (Vue 3):**
```json
{
  "dependencies": {
    "@tonconnect/ui": "^2.1.0",
    "pinia": "^3.0.3",
    "vue": "^3.5.13"
  }
}
```

## 🚀 Production Checklist

- [ ] Set up proper database with indexes
- [ ] Implement rate limiting
- [ ] Add request validation middleware
- [ ] Set up monitoring and logging
- [ ] Configure proper CORS settings
- [ ] Implement webhook signature verification
- [ ] Add transaction status polling/WebSocket
- [ ] Set up proper error tracking
- [ ] Configure load balancing
- [ ] Implement backup and recovery

## 🛡️ **Real TON SDK Implementation**

Here's the actual working implementation using `@ton/ton` and `@ton/core`:

```typescript
import { Cell, fromNano } from '@ton/core'
import { Address, TonClient } from '@ton/ton'

class TONBlockchainVerification {
  static async verifyTransaction(boc: string, expectedTransaction: TransactionData) {
    try {
      // 1. Validate BOC format
      if (!boc || boc.length < 10) {
        throw new Error('Invalid BOC format')
      }

      // 2. Parse BOC to validate format
      Cell.fromBase64(boc) // This will throw if invalid

      // 3. Initialize TON client
      const client = new TonClient({
        endpoint: 'https://toncenter.com/api/v2/jsonRPC',
        apiKey: process.env.TON_API_KEY || undefined // optional
      })

      // 4. Get recent transactions for destination address
      const destinationAddress = Address.parse(expectedTransaction.destinationAddress)
      const transactions = await client.getTransactions(destinationAddress, { limit: 20 })

      // 5. Find matching transaction within time window
      const expectedAmount = expectedTransaction.amount
      const timeWindow = 15 * 60 * 1000 // 15 minutes
      const now = Date.now()

      let foundTransaction = null

      for (const tx of transactions) {
        const txTime = tx.now * 1000
        if (now - txTime > timeWindow)
          continue

        if (tx.inMessage?.info.type === 'internal') {
          const actualAmount = fromNano((tx.inMessage.info as any).value.coins)
          const amountDiff = Math.abs(Number(actualAmount) - Number(expectedAmount))

          if (amountDiff < 0.000001) { // 1 nanoton tolerance
            foundTransaction = tx
            break
          }
        }
      }

      if (!foundTransaction) {
        throw new Error('No matching transaction found on blockchain')
      }

      // 6. Validate transaction details
      const inMessage = foundTransaction.inMessage!
      const actualAmount = fromNano((inMessage.info as any).value.coins)
      const actualDestination = (inMessage.info as any).dest?.toString() || 'unknown'

      if (actualDestination !== expectedTransaction.destinationAddress) {
        throw new Error(`Address mismatch: expected ${expectedTransaction.destinationAddress}, got ${actualDestination}`)
      }

      // 7. Check confirmations
      const currentBlock = await client.getMasterchainInfo()
      const confirmations = Math.max(1, currentBlock.latestSeqno - foundTransaction.now)

      if (confirmations < 1) {
        throw new Error('Transaction not yet confirmed')
      }

      // 8. Return verification result
      return {
        txHash: foundTransaction.hash().toString('hex'),
        lt: foundTransaction.lt.toString(),
        source: (inMessage.info as any).src?.toString() || 'unknown',
        destination: actualDestination,
        amount: actualAmount,
        confirmed: true,
        confirmations: Math.min(confirmations, 10),
        blockTime: foundTransaction.now,
      }
    }
    catch (error) {
      throw new Error(`Transaction verification failed: ${error.message}`)
    }
  }
}
```

## 🛡️ **Transaction Validation Checklist**

Before processing any order, verify:

- [ ] **Transaction exists on blockchain** (not just client BOC)
- [ ] **Transaction is confirmed** (sufficient confirmations)
- [ ] **Destination address matches** your merchant wallet
- [ ] **Amount matches** the expected payment amount
- [ ] **Transaction is recent** (within validity window)
- [ ] **Transaction hasn't been processed before** (prevent replay attacks)
- [ ] **Transaction source** is from expected user wallet

## 📋 **Database Schema**

```sql
-- Transactions table
CREATE TABLE transactions (
  id VARCHAR(255) PRIMARY KEY,
  order_id VARCHAR(255) NOT NULL,
  user_id VARCHAR(255),
  product_id VARCHAR(255) NOT NULL,
  quantity INT NOT NULL,
  expected_amount DECIMAL(10,9) NOT NULL, -- TON amount
  destination_address VARCHAR(255) NOT NULL,
  status ENUM('pending', 'confirmed', 'failed', 'expired') DEFAULT 'pending',
  ton_tx_hash VARCHAR(255), -- Blockchain transaction hash
  ton_tx_lt BIGINT, -- Logical time for TON transaction
  confirmations INT DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  expires_at TIMESTAMP NOT NULL,
  UNIQUE KEY unique_ton_tx (ton_tx_hash), -- Prevent processing same TX twice
  INDEX idx_status (status),
  INDEX idx_user_id (user_id),
  INDEX idx_expires_at (expires_at)
);

-- Products table
CREATE TABLE products (
  id VARCHAR(255) PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  price DECIMAL(10,9) NOT NULL, -- Price in TON
  status ENUM('active', 'inactive') DEFAULT 'active',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Orders table (after successful payment)
CREATE TABLE orders (
  id VARCHAR(255) PRIMARY KEY,
  transaction_id VARCHAR(255) NOT NULL,
  user_id VARCHAR(255),
  product_id VARCHAR(255) NOT NULL,
  quantity INT NOT NULL,
  total_amount DECIMAL(10,9) NOT NULL,
  status ENUM('processing', 'completed', 'refunded') DEFAULT 'processing',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (transaction_id) REFERENCES transactions(id),
  FOREIGN KEY (product_id) REFERENCES products(id)
);
```

## Backend Implementation

### 1. **Transaction Preparation Endpoint**

```javascript
// POST /api/payments/prepare
app.post('/api/payments/prepare', async (req, res) => {
  try {
    const { productId, quantity, userId } = req.body

    // 1. Validate product exists and is active
    const product = await db.query(
      'SELECT * FROM products WHERE id = ? AND status = "active"',
      [productId]
    )

    if (!product) {
      return res.status(404).json({ error: 'Product not found' })
    }

    // 2. Calculate total amount
    const totalAmount = (product.price * quantity).toFixed(9)

    // 3. Create transaction record
    const transactionId = `tx_${Date.now()}_${crypto.randomBytes(8).toString('hex')}`
    const orderId = `order_${Date.now()}`

    await db.query(`
      INSERT INTO transactions (
        id, order_id, user_id, product_id, quantity,
        expected_amount, destination_address, expires_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      transactionId,
      orderId,
      userId,
      productId,
      quantity,
      totalAmount,
      process.env.MERCHANT_WALLET_ADDRESS,
      new Date(Date.now() + 5 * 60 * 1000) // 5 minutes expiry
    ])

    res.json({
      id: transactionId,
      destinationAddress: process.env.MERCHANT_WALLET_ADDRESS,
      amount: totalAmount,
      description: `${product.name} x${quantity}`,
      validUntil: Math.floor(Date.now() / 1000) + 300,
      metadata: { productId, orderId, userId, quantity }
    })
  }
  catch (error) {
    res.status(500).json({ error: error.message })
  }
})
```

### 2. **Secure Transaction Confirmation**

```javascript
// POST /api/payments/confirm
app.post('/api/payments/confirm', async (req, res) => {
  try {
    const { transactionId, boc } = req.body

    // 1. Get expected transaction
    const [expectedTx] = await db.query(
      'SELECT * FROM transactions WHERE id = ? AND status = "pending"',
      [transactionId]
    )

    if (!expectedTx) {
      return res.status(404).json({ error: 'Transaction not found or already processed' })
    }

    // 2. Check expiry
    if (new Date() > expectedTx.expires_at) {
      await db.query('UPDATE transactions SET status = "expired" WHERE id = ?', [transactionId])
      return res.status(400).json({ error: 'Transaction expired' })
    }

    // 3. 🔐 CRITICAL: Verify on TON blockchain
    const tonClient = new TonClient({
      endpoint: process.env.TON_API_ENDPOINT,
      apiKey: process.env.TON_API_KEY
    })

    // Parse BOC and extract transaction details
    const cell = Cell.fromBoc(Buffer.from(boc, 'base64'))[0]
    const transaction = parseTransaction(cell)

    // Verify transaction exists on blockchain
    const onChainTx = await tonClient.getTransaction(
      Address.parse(transaction.address),
      transaction.lt.toString()
    )

    if (!onChainTx) {
      throw new Error('Transaction not found on blockchain')
    }

    // 4. Validate transaction details
    const inMessage = onChainTx.inMessage
    if (!inMessage || inMessage.dest.toString() !== expectedTx.destination_address) {
      throw new Error('Invalid destination address')
    }

    const actualAmount = fromNano(inMessage.value.coins)
    if (actualAmount !== expectedTx.expected_amount.toString()) {
      throw new Error(`Amount mismatch. Expected: ${expectedTx.expected_amount}, Got: ${actualAmount}`)
    }

    // 5. Check for sufficient confirmations
    const currentBlock = await tonClient.getMasterchainInfo()
    const confirmations = currentBlock.last.seqno - onChainTx.now

    if (confirmations < 1) {
      return res.status(202).json({
        status: 'pending',
        message: 'Waiting for confirmations',
        confirmations
      })
    }

    // 6. Prevent replay attacks - check if TX already processed
    const [existingTx] = await db.query(
      'SELECT id FROM transactions WHERE ton_tx_hash = ? AND id != ?',
      [onChainTx.hash, transactionId]
    )

    if (existingTx) {
      throw new Error('Transaction already processed')
    }

    // 7. Update transaction status
    await db.query(`
      UPDATE transactions
      SET status = 'confirmed', ton_tx_hash = ?, ton_tx_lt = ?, confirmations = ?, updated_at = NOW()
      WHERE id = ?
    `, [onChainTx.hash, onChainTx.lt.toString(), confirmations, transactionId])

    // 8. Process the order
    await processOrder(expectedTx)

    res.json({
      status: 'confirmed',
      txHash: onChainTx.hash,
      confirmations,
      orderId: expectedTx.order_id
    })
  }
  catch (error) {
    // Mark transaction as failed
    await db.query('UPDATE transactions SET status = "failed" WHERE id = ?', [transactionId])
    res.status(400).json({ error: error.message })
  }
})
```

### 3. **Order Processing**

```javascript
async function processOrder(transaction) {
  try {
    // 1. Create order record
    await db.query(`
      INSERT INTO orders (id, transaction_id, user_id, product_id, quantity, total_amount)
      VALUES (?, ?, ?, ?, ?, ?)
    `, [
      transaction.order_id,
      transaction.id,
      transaction.user_id,
      transaction.product_id,
      transaction.quantity,
      transaction.expected_amount
    ])

    // 2. Grant access/deliver digital goods
    switch (transaction.product_id) {
      case 'premium-features':
        await grantPremiumAccess(transaction.user_id, 30) // 30 days
        break
      case 'nft-purchase':
        await mintNFT(transaction.user_id, transaction.order_id)
        break
      case 'donation':
        await recordDonation(transaction.user_id, transaction.expected_amount)
        break
    }

    // 3. Send confirmation notification
    await sendOrderConfirmation(transaction.user_id, transaction.order_id)

    console.log(`✅ Order processed: ${transaction.order_id}`)
  }
  catch (error) {
    console.error('Order processing failed:', error)
    // You might want to retry or mark for manual review
    throw error
  }
}
```

## Required Dependencies

For the real TON verification, install these packages:

```bash
npm install @ton/ton @ton/core @ton/crypto
npm install --save-dev @types/node
```

## Production Deployment

### Environment Variables

```bash
# .env
NODE_ENV=production
PORT=3000

# TON Configuration
TON_API_ENDPOINT=https://toncenter.com/api/v2/jsonRPC
TON_API_KEY=your-toncenter-api-key
MERCHANT_WALLET_ADDRESS=EQABa48hjKzg09hN_HjxOic7r8T1PleIy1dRd8NvZ3922MP0

# Database
DATABASE_URL=mysql://user:pass@localhost:3306/payments
REDIS_URL=redis://localhost:6379

# Security
JWT_SECRET=your-jwt-secret
RATE_LIMIT_WINDOW_MS=900000  # 15 minutes
RATE_LIMIT_MAX_REQUESTS=100  # per window per IP
```

### Security Best Practices

1. **Rate Limiting**: Protect against spam/DoS attacks
2. **Input Validation**: Validate all request data
3. **Database Security**: Use prepared statements, connection pooling
4. **Monitoring**: Log all payment attempts and failures
5. **Backup Strategy**: Regular database backups
6. **SSL/TLS**: Always use HTTPS in production
7. **Webhook Validation**: If using TON webhooks, validate signatures

### Monitoring & Alerts

```javascript
// Monitor failed transactions
setInterval(async () => {
  const [failedTxs] = await db.query(`
    SELECT COUNT(*) as count
    FROM transactions
    WHERE status = 'failed' AND created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)
  `)

  if (failedTxs.count > 10) {
    await alertAdmins(`High failure rate: ${failedTxs.count} failed transactions in last hour`)
  }
}, 5 * 60 * 1000) // Check every 5 minutes
```

## Testing

### Test Transaction Verification

```javascript
// test/payment.test.js
describe('Payment Verification', () => {
  it('should reject invalid BOC data', async () => {
    const response = await request(app)
      .post('/api/payments/confirm')
      .send({
        transactionId: 'test-tx-1',
        boc: 'invalid-boc-data'
      })

    expect(response.status).toBe(400)
    expect(response.body.error).toContain('Invalid BOC')
  })

  it('should reject transaction with wrong amount', async () => {
    // Test with BOC containing wrong amount
    // This should be rejected by amount validation
  })

  it('should reject replay attacks', async () => {
    // Test using same transaction hash twice
    // Second attempt should be rejected
  })
})
```

---

## Key Security Takeaways

1. **🚨 Never trust client data** - Always verify on blockchain
2. **🔐 Use proper TON SDK** - Parse and validate BOC properly
3. **🛡️ Validate everything** - Amount, destination, confirmations
4. **🚫 Prevent replays** - Track processed transaction hashes
5. **⏰ Implement expiry** - Don't allow old transactions
6. **📊 Monitor actively** - Alert on unusual patterns

Following this guide ensures your TON payment integration is secure and production-ready!
