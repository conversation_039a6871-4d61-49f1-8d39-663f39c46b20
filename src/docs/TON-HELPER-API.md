# TON Helper API

This document outlines API endpoints related to TON (The Open Network) helper functions.

## Get TON Price

- **Endpoint:** `GET /api/ton-price`
- **Description:** Retrieves the current price of 1 TON coin in USD. The price is fetched from an external service (CoinGecko).
- **Controller Action:** `App\Http\Controllers\Api\TonPaymentController@tonPrice`

### Successful Response (200 OK)

The response body will contain the price of TON in USD.

```json
{
  "success": true,
  "message": "TON price fetched successfully",
  "data": 7.5
}
```

### Error Response

If the external API is unreachable or returns an error, the server will likely respond with a `500 Internal Server Error`.
