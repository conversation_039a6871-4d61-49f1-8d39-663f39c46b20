# Coins Mined Calculation Documentation

## Overview

This document explains the `calculatePendingTokens` logic for determining how many coins a user has mined based on their mining speed and elapsed time.

## Core Formula

```
coinsMined = (userSpeed * secondsElapsed) / GAS_PER_SECOND
```

Where:
- `userSpeed`: The user's current mining speed (coins per second in gas units)
- `secondsElapsed`: Time elapsed since the last checkpoint in seconds
- `GAS_PER_SECOND`: Constant value of 1,000,000 (used for precision)

## TypeScript Implementation

### Constants
```typescript
const GAS_PER_SECOND = 1_000_000
```

### Types
```typescript
interface UserMiningData {
  speed: number
  lastCheckpoint: Date
  boostExpiredAt?: Date
}
```

### Core Function
```typescript
function calculatePendingTokens(user: UserMiningData, endTime?: Date): number {
  const now = endTime ?? new Date()
  const lastCheckpoint = user.lastCheckpoint ?? now

  const secondsElapsed = Math.floor((now.getTime() - lastCheckpoint.getTime()) / 1000)

  if (secondsElapsed <= 0) {
    return 0
  }

  const coinsMined = (user.speed * secondsElapsed) / GAS_PER_SECOND

  return coinsMined
}
```

## Usage Examples

### Example 1: Regular Mining
```typescript
const user: UserMiningData = {
  speed: 5_000_000, // 5 coins per second in gas units
  lastCheckpoint: new Date(Date.now() - 3600 * 1000), // 1 hour ago
}

const coinsMined = calculatePendingTokens(user)
// Result: 5_000_000 * 3600 / 1_000_000 = 18 coins
```

## Key Features

1. **Flexible End Time**: Can calculate for any time period by specifying `endTime`
2. **Safety Check**: Returns 0 if no time has elapsed
3. **Precision**: Uses integer arithmetic with GAS_PER_SECOND for precision
4. **No Side Effects**: Pure calculation function

## Error Handling

- Returns `0` if `secondsElapsed <= 0`
- Handles null `lastCheckpoint` by using current time
- No division by zero risk (GAS_PER_SECOND is constant)
