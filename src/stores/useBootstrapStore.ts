import type { ClientSettings } from '@/types/settings'
import { defineStore } from 'pinia'
import { ref } from 'vue'
import { getClientSettings } from '@/repositories/settings'

export const useBootstrapStore = defineStore('bootstrap', () => {
  // Bootstrap state
  const isInitialized = ref(false)
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // Client settings state
  const clientSettings = ref<ClientSettings | null>(null)

  // Initialize the bootstrap data
  async function initialize() {
    if (isInitialized.value)
      return

    isLoading.value = true
    error.value = null

    try {
      // Load client settings
      clientSettings.value = await getClientSettings()

      // Mark as initialized
      isInitialized.value = true
    }
    catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to initialize app'
      console.error('Bootstrap initialization failed:', err)
    }
    finally {
      isLoading.value = false
    }
  }

  // Reset initialization state
  function reset() {
    isInitialized.value = false
    isLoading.value = false
    error.value = null
    clientSettings.value = null
  }

  return {
    // State
    isInitialized,
    isLoading,
    error,
    clientSettings,

    // Actions
    initialize,
    reset,
  }
})
