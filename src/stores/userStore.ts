import type { UserData } from '@/types/user'
import { defineStore } from 'pinia'
import { computed, ref } from 'vue'
import { me } from '@/repositories/user'

export const useUserStore = defineStore('user', () => {
  const user = ref<UserData | null>(null)
  const accessToken = ref<string | null>(null)
  const tokenExpiresAt = ref<string | null>(null)

  const isTokenExpired = computed(() => {
    if (!tokenExpiresAt.value)
      return true
    return new Date() >= new Date(tokenExpiresAt.value)
  })

  // Computed property to check if user is authenticated
  const isAuthenticated = computed(() => {
    return !!(accessToken.value && !isTokenExpired.value)
  })

  function setUser(userData: UserData) {
    user.value = userData
  }

  function setAccessToken(token: string) {
    accessToken.value = token
  }

  function setTokenExpiresAt(expiresAt: string) {
    tokenExpiresAt.value = expiresAt
  }

  // Set authentication data from API response
  function setAuthData(token: string, expiresAt: string, userData?: UserData) {
    setAccessToken(token)
    setTokenExpiresAt(expiresAt)
    if (userData) {
      setUser(userData)
    }
  }

  // Clear all authentication data
  function clearAuthData() {
    user.value = null
    accessToken.value = null
    tokenExpiresAt.value = null
  }

  // Get time until token expires (in milliseconds)
  function getTimeUntilExpiry(): number {
    if (!tokenExpiresAt.value)
      return 0
    return new Date(tokenExpiresAt.value).getTime() - new Date().getTime()
  }

  async function refreshUserData() {
    if (isAuthenticated.value) {
      const response = await me()
      setUser(response)
    }
  }

  return {
    user,
    accessToken,
    tokenExpiresAt,
    isTokenExpired,
    isAuthenticated,

    setUser,
    setAccessToken,
    setTokenExpiresAt,
    setAuthData,
    clearAuthData,
    getTimeUntilExpiry,
    refreshUserData,
  }
})
