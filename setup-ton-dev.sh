#!/bin/bash

# TON SDK Development Setup Script
# This script automates the process of setting up localhost.run tunnel and updating configuration files

set -e  # Exit on any error

echo "🚀 Setting up TON SDK development environment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
LOCAL_PORT=5173
TUNNEL_TIMEOUT=30
MANIFEST_FILE="public/tonconnect-manifest.json"
ENV_FILE=".env"
BOT_ENV_FILE="../bot/.env"

# Function to cleanup on exit
cleanup() {
    echo -e "\n${YELLOW}🧹 Cleaning up...${NC}"
    if [ ! -z "$SSH_PID" ]; then
        kill $SSH_PID 2>/dev/null || true
        echo "✅ Tunnel process terminated"
    fi
}

# Set trap to cleanup on script exit
trap cleanup EXIT INT TERM

# Function to extract URL from tunnel output
extract_tunnel_url() {
    local output_file=$1
    local url=""
    local attempts=0
    local max_attempts=30

    echo "🔍 Waiting for tunnel URL..."

    while [ $attempts -lt $max_attempts ]; do
        if [ -f "$output_file" ]; then
            # Look for the tunneled line with HTTPS URL
            url=$(grep -oP 'https://[a-zA-Z0-9]+\.lhr\.life' "$output_file" | head -1)
            if [ ! -z "$url" ]; then
                echo -e "${GREEN}✅ Found tunnel URL: $url${NC}"
                echo "$url"
                return 0
            fi
        fi
        sleep 1
        attempts=$((attempts + 1))
        echo -n "."
    done

    echo -e "\n${RED}❌ Failed to extract tunnel URL after $max_attempts seconds${NC}"
    return 1
}

# Function to update tonconnect manifest
update_manifest() {
    local url=$1
    echo "📝 Updating tonconnect manifest..."

    if [ ! -f "$MANIFEST_FILE" ]; then
        echo -e "${YELLOW}⚠️ Creating new manifest file${NC}"
        mkdir -p public
    fi

    cat > "$MANIFEST_FILE" << EOF
{
  "url": "$url",
  "name": "TonConnect UI",
  "iconUrl": "$url/tonconnect-icon.png",
  "termsOfUseUrl": "$url/terms-of-use.txt",
  "privacyPolicyUrl": "$url/privacy-policy.txt"
}
EOF

    echo -e "${GREEN}✅ Updated $MANIFEST_FILE${NC}"
}

# Function to update .env file with both VITE_APP_URL and MINI_APP_URL
update_env() {
    local url=$1
    echo "📝 Updating app .env file..."

    # Create a temporary file with the new URL
    local temp_file=$(mktemp)

    if [ -f "$ENV_FILE" ]; then
        # Remove existing VITE_APP_URL and MINI_APP_URL lines and add new ones
        grep -v "^VITE_APP_URL=" "$ENV_FILE" | grep -v "^MINI_APP_URL=" > "$temp_file" 2>/dev/null || true
        echo "VITE_APP_URL=$url" >> "$temp_file"
        echo "MINI_APP_URL=$url" >> "$temp_file"
        mv "$temp_file" "$ENV_FILE"
    else
        # Create new .env file
        echo "VITE_APP_URL=$url" > "$ENV_FILE"
        echo "MINI_APP_URL=$url" >> "$ENV_FILE"
    fi

    # Clean up temp file if it still exists
    rm -f "$temp_file" 2>/dev/null || true

    echo -e "${GREEN}✅ Updated $ENV_FILE${NC}"
}

# Function to update bot .env file
update_bot_env() {
    local url=$1
    echo "📝 Updating bot .env file..."

    if [ -f "$BOT_ENV_FILE" ]; then
        # Create a temporary file with the new URL
        local temp_file=$(mktemp)

        # Remove existing MINI_APP_URL line and add new one
        grep -v "^MINI_APP_URL=" "$BOT_ENV_FILE" > "$temp_file" 2>/dev/null || true
        echo "MINI_APP_URL=$url" >> "$temp_file"
        mv "$temp_file" "$BOT_ENV_FILE"

        # Clean up temp file if it still exists
        rm -f "$temp_file" 2>/dev/null || true

        echo -e "${GREEN}✅ Updated $BOT_ENV_FILE${NC}"
    else
        echo -e "${YELLOW}⚠️ Bot .env file not found, skipping bot configuration${NC}"
    fi
}

# Function to restart bot if it's running
restart_bot() {
    echo "🤖 Checking for running bot processes..."

    # Look for bot processes (assuming it's a Node.js or Python bot)
    local bot_pids=$(pgrep -f "bot" 2>/dev/null || true)

    if [ ! -z "$bot_pids" ]; then
        echo -e "${YELLOW}⚠️ Found running bot processes, attempting restart...${NC}"

        # Kill existing bot processes
        echo "$bot_pids" | xargs kill 2>/dev/null || true
        sleep 2

        # Try to start the bot if there's a start script
        if [ -f "../bot/package.json" ]; then
            echo "🚀 Starting Node.js bot..."
            (cd ../bot && npm start > /dev/null 2>&1 &) || echo -e "${YELLOW}⚠️ Could not auto-start bot. Please start it manually.${NC}"
        elif [ -f "../bot/main.py" ] || [ -f "../bot/bot.py" ]; then
            echo "🚀 Starting Python bot..."
            (cd ../bot && python3 main.py > /dev/null 2>&1 &) 2>/dev/null || \
            (cd ../bot && python3 bot.py > /dev/null 2>&1 &) 2>/dev/null || \
            echo -e "${YELLOW}⚠️ Could not auto-start bot. Please start it manually.${NC}"
        else
            echo -e "${YELLOW}⚠️ Bot restart completed. Please start your bot manually with the new URL.${NC}"
        fi
    else
        echo -e "${BLUE}ℹ️ No running bot processes found${NC}"
    fi
}

# Function to display QR code info
show_qr_info() {
    local url=$1
    echo -e "\n${BLUE}📱 Mobile Testing:${NC}"
    echo "Visit this URL on your mobile device: $url"
    echo "Or scan the QR code that appeared in the tunnel output above"
}

# Main execution
main() {
    echo -e "${BLUE}🔗 Starting localhost.run tunnel...${NC}"

    # Create temporary file for tunnel output
    TUNNEL_OUTPUT=$(mktemp)

    # Start SSH tunnel in background and capture output
    ssh -R 80:localhost:$LOCAL_PORT localhost.run > "$TUNNEL_OUTPUT" 2>&1 &
    SSH_PID=$!

    echo "🕒 Tunnel process started (PID: $SSH_PID)"
    echo "📡 Capturing tunnel output..."

    # Extract the tunnel URL
    TUNNEL_URL=$(extract_tunnel_url "$TUNNEL_OUTPUT")

    if [ $? -eq 0 ] && [ ! -z "$TUNNEL_URL" ]; then
        echo -e "\n${GREEN}🎉 Tunnel established successfully!${NC}"
        echo -e "${BLUE}Public URL: $TUNNEL_URL${NC}"

        # Update configuration files
        update_manifest "$TUNNEL_URL"
        update_env "$TUNNEL_URL"
        update_bot_env "$TUNNEL_URL"

        # Restart bot with new URL
        restart_bot

        # Show mobile testing info
        show_qr_info "$TUNNEL_URL"

        echo -e "\n${GREEN}✅ TON SDK development environment is ready!${NC}"
        echo -e "${YELLOW}📋 Next steps:${NC}"
        echo "   1. Run 'npm run dev' in another terminal to start your development server"
        echo "   2. Your app will be accessible at: $TUNNEL_URL"
        echo "   3. Test TonConnect functionality using the public URL"
        echo "   4. Bot has been updated with the new MINI_APP_URL"
        echo -e "\n${YELLOW}⚠️ Keep this terminal open to maintain the tunnel${NC}"
        echo -e "${BLUE}Press Ctrl+C to stop the tunnel${NC}"

        # Keep the tunnel running
        wait $SSH_PID
    else
        echo -e "${RED}❌ Failed to establish tunnel${NC}"
        exit 1
    fi

    # Cleanup temporary file
    rm -f "$TUNNEL_OUTPUT"
}

# Check if localhost.run is reachable
echo "🌐 Checking localhost.run connectivity..."
if ! curl -s --connect-timeout 5 localhost.run > /dev/null; then
    echo -e "${RED}❌ Cannot reach localhost.run. Please check your internet connection.${NC}"
    exit 1
fi

# Run main function
main
